Unity Editor version:    6000.0.39f1 (15ea7ed0b100)
Branch:                  6000.0/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.4 (Build 24E248)
Darwin version:          24.4.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        8192 MB
Using pre-set license
Pro License: YES

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MacOS/Unity
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
/Users/<USER>/UnityProjects/Soul_game
-logFile
Logs/AssetImportWorker1.log
-srvPort
53137
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: /Users/<USER>/UnityProjects/Soul_game
/Users/<USER>/UnityProjects/Soul_game
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8440450112]  Target information:

Player connection [8440450112]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2404513191 [EditorId] 2404513191 [Version] 1048832 [Id] OSXEditor(0,Georgis-MacBook-Air.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8440450112] Host joined multi-casting on [***********:54997]...
Player connection [8440450112] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 21.19 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.39f1 (15ea7ed0b100)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/UnityProjects/Soul_game/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
 preferred device: Apple M1 (high power)
Metal devices available: 1
0: Apple M1 (high power)
Using device Apple M1 (high power)
Initializing Metal device caps: Apple M1
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56030
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.39f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Registered in 0.002538 seconds.
- Loaded All Assemblies, in  0.462 seconds
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.474 seconds
Domain Reload Profiling: 937ms
	BeginReloadAssembly (150ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (55ms)
	LoadAllAssembliesAndSetupDomain (206ms)
		LoadAssemblies (151ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (201ms)
			TypeCache.Refresh (198ms)
				TypeCache.ScanAssembly (168ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (475ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (409ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (49ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (92ms)
			ProcessInitializeOnLoadAttributes (206ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Asset path could not be found for script compilation file '/Assets/LightningAudio.cs'
- Loaded All Assemblies, in  1.078 seconds
Refreshing native plugins compatible for Editor in 2.38 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.55 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.984 seconds
Domain Reload Profiling: 2063ms
	BeginReloadAssembly (207ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (79ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (781ms)
		LoadAssemblies (510ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (355ms)
			TypeCache.Refresh (271ms)
				TypeCache.ScanAssembly (239ms)
			BuildScriptInfoCaches (66ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (985ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (759ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (108ms)
			ProcessInitializeOnLoadAttributes (537ms)
			ProcessInitializeOnLoadMethodAttributes (98ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Launching external process: /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.31 seconds
Refreshing native plugins compatible for Editor in 1.90 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 212 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6160 unused Assets / (2.7 MB). Loaded Objects now: 6819.
Memory consumption went from 172.6 MB to 169.9 MB.
Total: 38.407666 ms (FindLiveObjects: 3.296125 ms CreateObjectMapping: 0.489542 ms MarkObjects: 30.057083 ms  DeleteObjects: 4.564292 ms)

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17f81f000 may have been prematurely finalized
- Loaded All Assemblies, in  1.003 seconds
Refreshing native plugins compatible for Editor in 3.27 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.60 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.960 seconds
Domain Reload Profiling: 1969ms
	BeginReloadAssembly (307ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (23ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (116ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (632ms)
		LoadAssemblies (476ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (248ms)
			TypeCache.Refresh (35ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (187ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (961ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (710ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (527ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.26 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.1 MB). Loaded Objects now: 6834.
Memory consumption went from 163.2 MB to 161.1 MB.
Total: 17.691250 ms (FindLiveObjects: 1.037375 ms CreateObjectMapping: 0.497209 ms MarkObjects: 12.521916 ms  DeleteObjects: 3.633458 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17f393000 may have been prematurely finalized
- Loaded All Assemblies, in  1.187 seconds
Refreshing native plugins compatible for Editor in 2.47 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.60 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.619 seconds
Domain Reload Profiling: 1809ms
	BeginReloadAssembly (418ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (32ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (103ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (691ms)
		LoadAssemblies (573ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (275ms)
			TypeCache.Refresh (30ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (228ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (620ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (475ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (358ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.26 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.2 MB). Loaded Objects now: 6836.
Memory consumption went from 161.5 MB to 157.3 MB.
Total: 12.410250 ms (FindLiveObjects: 2.778458 ms CreateObjectMapping: 0.480875 ms MarkObjects: 6.237084 ms  DeleteObjects: 2.913250 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.809 seconds
Refreshing native plugins compatible for Editor in 1.84 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.22 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.618 seconds
Domain Reload Profiling: 1429ms
	BeginReloadAssembly (229ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (101ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (518ms)
		LoadAssemblies (350ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (221ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (181ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (618ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (484ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (359ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 2.18 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.6 MB). Loaded Objects now: 6838.
Memory consumption went from 161.5 MB to 158.9 MB.
Total: 8.953667 ms (FindLiveObjects: 0.605458 ms CreateObjectMapping: 0.355542 ms MarkObjects: 6.172167 ms  DeleteObjects: 1.820000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.916 seconds
Refreshing native plugins compatible for Editor in 2.44 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.13 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.723 seconds
Domain Reload Profiling: 1644ms
	BeginReloadAssembly (333ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (42ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (115ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (522ms)
		LoadAssemblies (429ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (196ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (169ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (723ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (573ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (72ms)
			ProcessInitializeOnLoadAttributes (436ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 1.89 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.7 MB). Loaded Objects now: 6840.
Memory consumption went from 161.5 MB to 158.8 MB.
Total: 10.916542 ms (FindLiveObjects: 0.545083 ms CreateObjectMapping: 0.388041 ms MarkObjects: 8.092542 ms  DeleteObjects: 1.889666 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  1.381 seconds
Refreshing native plugins compatible for Editor in 2.00 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.09 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.657 seconds
Domain Reload Profiling: 2044ms
	BeginReloadAssembly (540ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (58ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (134ms)
	RebuildCommonClasses (47ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (750ms)
		LoadAssemblies (716ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (245ms)
			TypeCache.Refresh (29ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (196ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (658ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (499ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (78ms)
			ProcessInitializeOnLoadAttributes (373ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.89 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.9 MB). Loaded Objects now: 6842.
Memory consumption went from 161.3 MB to 158.4 MB.
Total: 10.684125 ms (FindLiveObjects: 0.594917 ms CreateObjectMapping: 0.292375 ms MarkObjects: 7.857500 ms  DeleteObjects: 1.939042 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  1.108 seconds
Refreshing native plugins compatible for Editor in 3.04 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.51 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.657 seconds
Domain Reload Profiling: 1771ms
	BeginReloadAssembly (400ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (35ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (126ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (623ms)
		LoadAssemblies (510ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (247ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (206ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (658ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (495ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (359ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 1.95 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.6 MB). Loaded Objects now: 6844.
Memory consumption went from 161.3 MB to 157.8 MB.
Total: 7.708708 ms (FindLiveObjects: 0.549667 ms CreateObjectMapping: 0.371125 ms MarkObjects: 4.987583 ms  DeleteObjects: 1.799750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  1.008 seconds
Refreshing native plugins compatible for Editor in 10.73 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.32 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.759 seconds
Domain Reload Profiling: 1772ms
	BeginReloadAssembly (312ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (136ms)
	RebuildCommonClasses (56ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (606ms)
		LoadAssemblies (411ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (309ms)
			TypeCache.Refresh (34ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (256ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (759ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (604ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (74ms)
			ProcessInitializeOnLoadAttributes (450ms)
			ProcessInitializeOnLoadMethodAttributes (70ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 3.58 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.6 MB). Loaded Objects now: 6846.
Memory consumption went from 161.3 MB to 158.8 MB.
Total: 14.601458 ms (FindLiveObjects: 0.572083 ms CreateObjectMapping: 0.680500 ms MarkObjects: 11.317916 ms  DeleteObjects: 2.029583 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.851 seconds
Refreshing native plugins compatible for Editor in 2.10 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.78 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.685 seconds
Domain Reload Profiling: 1538ms
	BeginReloadAssembly (254ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (111ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (514ms)
		LoadAssemblies (325ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (251ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (217ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (685ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (544ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (71ms)
			ProcessInitializeOnLoadAttributes (411ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 4.16 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.9 MB). Loaded Objects now: 6848.
Memory consumption went from 161.3 MB to 157.4 MB.
Total: 24.001959 ms (FindLiveObjects: 1.869958 ms CreateObjectMapping: 0.825334 ms MarkObjects: 19.582458 ms  DeleteObjects: 1.723584 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.777 seconds
Refreshing native plugins compatible for Editor in 2.33 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.43 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.779 seconds
Domain Reload Profiling: 1560ms
	BeginReloadAssembly (279ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (149ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (438ms)
		LoadAssemblies (314ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (185ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (146ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (779ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (600ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (438ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.50 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.6 MB). Loaded Objects now: 6850.
Memory consumption went from 161.3 MB to 158.7 MB.
Total: 13.853209 ms (FindLiveObjects: 0.565792 ms CreateObjectMapping: 0.409083 ms MarkObjects: 10.746000 ms  DeleteObjects: 2.131958 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.736 seconds
Refreshing native plugins compatible for Editor in 2.62 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.97 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.762 seconds
Domain Reload Profiling: 1500ms
	BeginReloadAssembly (229ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (125ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (437ms)
		LoadAssemblies (297ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (184ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (147ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (762ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (574ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (434ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.8 MB). Loaded Objects now: 6852.
Memory consumption went from 161.3 MB to 158.5 MB.
Total: 9.758334 ms (FindLiveObjects: 0.585584 ms CreateObjectMapping: 0.382709 ms MarkObjects: 6.854625 ms  DeleteObjects: 1.934708 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.706 seconds
Refreshing native plugins compatible for Editor in 1.89 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.30 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.634 seconds
Domain Reload Profiling: 1344ms
	BeginReloadAssembly (213ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (3ms)
		CreateAndSetChildDomain (92ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (438ms)
		LoadAssemblies (253ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (229ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (198ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (635ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (493ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (74ms)
			ProcessInitializeOnLoadAttributes (370ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 2.08 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.5 MB). Loaded Objects now: 6854.
Memory consumption went from 161.3 MB to 157.8 MB.
Total: 7.581791 ms (FindLiveObjects: 0.503042 ms CreateObjectMapping: 0.288625 ms MarkObjects: 5.246000 ms  DeleteObjects: 1.543375 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 216974.045437 seconds.
  path: Assets/Materials/SoulCreatureGiant.mat
  artifactKey: Guid(9d2a0b86b9b4442a4bbfecfcb46449e3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/SoulCreatureGiant.mat using Guid(9d2a0b86b9b4442a4bbfecfcb46449e3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3d9711d9c25a9d63a6770a4a11dbf620') in 0.808423917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  1.005 seconds
Refreshing native plugins compatible for Editor in 3.24 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.60 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.871 seconds
Domain Reload Profiling: 1880ms
	BeginReloadAssembly (353ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (42ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (110ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (541ms)
		LoadAssemblies (463ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (208ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (178ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (872ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (664ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (489ms)
			ProcessInitializeOnLoadMethodAttributes (67ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 1.93 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.9 MB). Loaded Objects now: 6922.
Memory consumption went from 169.1 MB to 165.2 MB.
Total: 20.118625 ms (FindLiveObjects: 1.331500 ms CreateObjectMapping: 0.488042 ms MarkObjects: 13.736833 ms  DeleteObjects: 4.561875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.716 seconds
Refreshing native plugins compatible for Editor in 2.35 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.76 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.625 seconds
Domain Reload Profiling: 1345ms
	BeginReloadAssembly (225ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (99ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (434ms)
		LoadAssemblies (226ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (253ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (225ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (625ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (490ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (71ms)
			ProcessInitializeOnLoadAttributes (366ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.09 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.8 MB). Loaded Objects now: 6924.
Memory consumption went from 168.8 MB to 165.0 MB.
Total: 7.503792 ms (FindLiveObjects: 0.651125 ms CreateObjectMapping: 0.269000 ms MarkObjects: 4.987834 ms  DeleteObjects: 1.595500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.750 seconds
Refreshing native plugins compatible for Editor in 2.42 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.29 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.634 seconds
Domain Reload Profiling: 1387ms
	BeginReloadAssembly (228ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (33ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (97ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (457ms)
		LoadAssemblies (239ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (264ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (216ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (634ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (497ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (70ms)
			ProcessInitializeOnLoadAttributes (374ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 1.94 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.4 MB). Loaded Objects now: 6926.
Memory consumption went from 168.8 MB to 165.4 MB.
Total: 7.463792 ms (FindLiveObjects: 0.580416 ms CreateObjectMapping: 0.266417 ms MarkObjects: 5.163916 ms  DeleteObjects: 1.452417 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.28 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6083 unused Assets / (2.8 MB). Loaded Objects now: 6926.
Memory consumption went from 158.9 MB to 156.1 MB.
Total: 33.920542 ms (FindLiveObjects: 0.546333 ms CreateObjectMapping: 0.353375 ms MarkObjects: 29.885541 ms  DeleteObjects: 3.134583 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.756 seconds
Refreshing native plugins compatible for Editor in 1.89 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.19 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.626 seconds
Domain Reload Profiling: 1385ms
	BeginReloadAssembly (237ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (99ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (456ms)
		LoadAssemblies (243ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (264ms)
			TypeCache.Refresh (27ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (225ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (626ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (478ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (74ms)
			ProcessInitializeOnLoadAttributes (355ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.82 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.0 MB). Loaded Objects now: 6928.
Memory consumption went from 168.7 MB to 165.7 MB.
Total: 14.702042 ms (FindLiveObjects: 0.717542 ms CreateObjectMapping: 0.414750 ms MarkObjects: 11.385500 ms  DeleteObjects: 2.184000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.728 seconds
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.30 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.885 seconds
Domain Reload Profiling: 1616ms
	BeginReloadAssembly (208ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (99ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (53ms)
	LoadAllAssembliesAndSetupDomain (425ms)
		LoadAssemblies (281ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (191ms)
			TypeCache.Refresh (23ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (156ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (885ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (650ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (116ms)
			ProcessInitializeOnLoadAttributes (478ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 3.40 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.9 MB). Loaded Objects now: 6930.
Memory consumption went from 168.8 MB to 164.9 MB.
Total: 11.318625 ms (FindLiveObjects: 0.671833 ms CreateObjectMapping: 0.464000 ms MarkObjects: 7.467792 ms  DeleteObjects: 2.714209 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1178.782226 seconds.
  path: Assets/Kamgam/SkyCloudsURP/Runtime/Materials/ThirdParty/SkyClouds_StylizedClouds.mat
  artifactKey: Guid(8dd590b390be4264a87b624e0980e669) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Kamgam/SkyCloudsURP/Runtime/Materials/ThirdParty/SkyClouds_StylizedClouds.mat using Guid(8dd590b390be4264a87b624e0980e669) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b66336034b7a1148648c65ed1ca5000e') in 0.404211666 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.776 seconds
Refreshing native plugins compatible for Editor in 1.87 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.07 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.723 seconds
Domain Reload Profiling: 1501ms
	BeginReloadAssembly (239ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (36ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (86ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (450ms)
		LoadAssemblies (337ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (169ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (141ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (723ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (582ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (85ms)
			ProcessInitializeOnLoadAttributes (443ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 3.76 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.4 MB). Loaded Objects now: 6952.
Memory consumption went from 173.2 MB to 169.8 MB.
Total: 17.634083 ms (FindLiveObjects: 2.844584 ms CreateObjectMapping: 0.500875 ms MarkObjects: 10.623917 ms  DeleteObjects: 3.663542 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.657 seconds
Refreshing native plugins compatible for Editor in 1.81 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.06 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.614 seconds
Domain Reload Profiling: 1275ms
	BeginReloadAssembly (208ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (98ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (381ms)
		LoadAssemblies (230ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (190ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (164ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (614ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (475ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (347ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.10 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.0 MB). Loaded Objects now: 6954.
Memory consumption went from 172.8 MB to 168.8 MB.
Total: 8.786458 ms (FindLiveObjects: 1.122875 ms CreateObjectMapping: 0.356083 ms MarkObjects: 5.257125 ms  DeleteObjects: 2.049709 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.737 seconds
Refreshing native plugins compatible for Editor in 2.18 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.619 seconds
Domain Reload Profiling: 1360ms
	BeginReloadAssembly (217ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (34ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (92ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (452ms)
		LoadAssemblies (215ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (273ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (240ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (619ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (484ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (72ms)
			ProcessInitializeOnLoadAttributes (359ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.02 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.7 MB). Loaded Objects now: 6956.
Memory consumption went from 172.8 MB to 170.1 MB.
Total: 7.928917 ms (FindLiveObjects: 0.728959 ms CreateObjectMapping: 0.321541 ms MarkObjects: 5.255334 ms  DeleteObjects: 1.622375 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.811 seconds
Refreshing native plugins compatible for Editor in 9.35 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 5.68 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.219 seconds
Domain Reload Profiling: 2036ms
	BeginReloadAssembly (263ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (97ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (480ms)
		LoadAssemblies (355ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (223ms)
			TypeCache.Refresh (28ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (173ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1221ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1028ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (206ms)
			ProcessInitializeOnLoadAttributes (745ms)
			ProcessInitializeOnLoadMethodAttributes (65ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.18 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.3 MB). Loaded Objects now: 6958.
Memory consumption went from 172.8 MB to 169.5 MB.
Total: 15.585000 ms (FindLiveObjects: 0.727542 ms CreateObjectMapping: 0.422167 ms MarkObjects: 11.314250 ms  DeleteObjects: 3.120541 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.653 seconds
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 5.48 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.613 seconds
Domain Reload Profiling: 1271ms
	BeginReloadAssembly (229ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (94ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (357ms)
		LoadAssemblies (239ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (174ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (144ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (615ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (477ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (355ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.83 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.0 MB). Loaded Objects now: 6960.
Memory consumption went from 172.8 MB to 169.8 MB.
Total: 14.043250 ms (FindLiveObjects: 0.688375 ms CreateObjectMapping: 0.281500 ms MarkObjects: 9.335000 ms  DeleteObjects: 3.736958 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  1.025 seconds
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.79 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.781 seconds
Domain Reload Profiling: 1809ms
	BeginReloadAssembly (416ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (38ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (111ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (549ms)
		LoadAssemblies (475ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (213ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (174ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (781ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (614ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (476ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.99 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.1 MB). Loaded Objects now: 6962.
Memory consumption went from 172.8 MB to 169.7 MB.
Total: 15.610042 ms (FindLiveObjects: 0.816791 ms CreateObjectMapping: 0.414250 ms MarkObjects: 10.844250 ms  DeleteObjects: 3.534083 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  1.194 seconds
Refreshing native plugins compatible for Editor in 2.99 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.666 seconds
Domain Reload Profiling: 1864ms
	BeginReloadAssembly (412ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (37ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (118ms)
	RebuildCommonClasses (55ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (697ms)
		LoadAssemblies (564ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (286ms)
			TypeCache.Refresh (33ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (233ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (666ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (499ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (373ms)
			ProcessInitializeOnLoadMethodAttributes (35ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.8 MB). Loaded Objects now: 6964.
Memory consumption went from 172.8 MB to 169.0 MB.
Total: 10.891083 ms (FindLiveObjects: 0.616000 ms CreateObjectMapping: 0.352708 ms MarkObjects: 7.398459 ms  DeleteObjects: 2.523542 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.748 seconds
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.45 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.720 seconds
Domain Reload Profiling: 1470ms
	BeginReloadAssembly (276ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (119ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (411ms)
		LoadAssemblies (286ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (188ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (162ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (720ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (581ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (449ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 1.95 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.5 MB). Loaded Objects now: 6966.
Memory consumption went from 172.8 MB to 169.4 MB.
Total: 11.937417 ms (FindLiveObjects: 1.405041 ms CreateObjectMapping: 0.555667 ms MarkObjects: 7.162375 ms  DeleteObjects: 2.797042 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  1.230 seconds
Refreshing native plugins compatible for Editor in 1.99 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.15 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.654 seconds
Domain Reload Profiling: 1887ms
	BeginReloadAssembly (607ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (41ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (144ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (550ms)
		LoadAssemblies (628ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (213ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (175ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (654ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (496ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (366ms)
			ProcessInitializeOnLoadMethodAttributes (35ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.05 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.0 MB). Loaded Objects now: 6968.
Memory consumption went from 172.8 MB to 168.8 MB.
Total: 7.752125 ms (FindLiveObjects: 0.464875 ms CreateObjectMapping: 0.297250 ms MarkObjects: 5.061666 ms  DeleteObjects: 1.927583 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.686 seconds
Refreshing native plugins compatible for Editor in 2.14 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.22 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.622 seconds
Domain Reload Profiling: 1311ms
	BeginReloadAssembly (232ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (104ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (394ms)
		LoadAssemblies (249ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (187ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (164ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (623ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (472ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (77ms)
			ProcessInitializeOnLoadAttributes (351ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.12 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (6.0 MB). Loaded Objects now: 6970.
Memory consumption went from 172.8 MB to 166.8 MB.
Total: 22.679291 ms (FindLiveObjects: 0.628417 ms CreateObjectMapping: 0.497667 ms MarkObjects: 16.650500 ms  DeleteObjects: 4.902208 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  1.116 seconds
Refreshing native plugins compatible for Editor in 1.93 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.32 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.658 seconds
Domain Reload Profiling: 1779ms
	BeginReloadAssembly (464ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (59ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (4ms)
		CreateAndSetChildDomain (118ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (579ms)
		LoadAssemblies (504ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (253ms)
			TypeCache.Refresh (29ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (195ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (658ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (490ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (92ms)
			ProcessInitializeOnLoadAttributes (354ms)
			ProcessInitializeOnLoadMethodAttributes (35ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.50 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.5 MB). Loaded Objects now: 6972.
Memory consumption went from 172.8 MB to 169.4 MB.
Total: 10.031667 ms (FindLiveObjects: 0.577417 ms CreateObjectMapping: 0.381625 ms MarkObjects: 6.750083 ms  DeleteObjects: 2.321792 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.846 seconds
Refreshing native plugins compatible for Editor in 5.65 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.87 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.721 seconds
Domain Reload Profiling: 1572ms
	BeginReloadAssembly (230ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (103ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (560ms)
		LoadAssemblies (276ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (324ms)
			TypeCache.Refresh (30ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (280ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (723ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (568ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (79ms)
			ProcessInitializeOnLoadAttributes (434ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 3.89 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.1 MB). Loaded Objects now: 6974.
Memory consumption went from 172.8 MB to 170.8 MB.
Total: 23.952250 ms (FindLiveObjects: 0.969000 ms CreateObjectMapping: 0.442000 ms MarkObjects: 15.583292 ms  DeleteObjects: 6.954917 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  2.600 seconds
Refreshing native plugins compatible for Editor in 3.12 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 9.97 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.582 seconds
Domain Reload Profiling: 4194ms
	BeginReloadAssembly (1249ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (73ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (3ms)
		CreateAndSetChildDomain (489ms)
	RebuildCommonClasses (83ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (1212ms)
		LoadAssemblies (1014ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (521ms)
			TypeCache.Refresh (152ms)
				TypeCache.ScanAssembly (24ms)
			BuildScriptInfoCaches (335ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (1590ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1247ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (160ms)
			ProcessInitializeOnLoadAttributes (961ms)
			ProcessInitializeOnLoadMethodAttributes (110ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (31ms)
Refreshing native plugins compatible for Editor in 2.76 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.3 MB). Loaded Objects now: 6976.
Memory consumption went from 172.8 MB to 169.5 MB.
Total: 55.521416 ms (FindLiveObjects: 5.592083 ms CreateObjectMapping: 1.152375 ms MarkObjects: 41.004291 ms  DeleteObjects: 7.771209 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.957 seconds
Refreshing native plugins compatible for Editor in 3.02 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.99 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.686 seconds
Domain Reload Profiling: 1649ms
	BeginReloadAssembly (350ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (44ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (150ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (550ms)
		LoadAssemblies (286ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (339ms)
			TypeCache.Refresh (39ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (281ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (687ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (502ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (77ms)
			ProcessInitializeOnLoadAttributes (361ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.13 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.6 MB). Loaded Objects now: 6978.
Memory consumption went from 172.8 MB to 168.2 MB.
Total: 11.004500 ms (FindLiveObjects: 1.394292 ms CreateObjectMapping: 0.376125 ms MarkObjects: 6.639500 ms  DeleteObjects: 2.593916 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  5.453 seconds
Refreshing native plugins compatible for Editor in 2.95 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.43 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.761 seconds
Domain Reload Profiling: 6222ms
	BeginReloadAssembly (3169ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (194ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (15ms)
		CreateAndSetChildDomain (959ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (2212ms)
		LoadAssemblies (3147ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (484ms)
			TypeCache.Refresh (132ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (315ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (761ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (597ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (440ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 3.21 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (2.4 MB). Loaded Objects now: 6980.
Memory consumption went from 172.9 MB to 170.5 MB.
Total: 46.719334 ms (FindLiveObjects: 7.133792 ms CreateObjectMapping: 0.578417 ms MarkObjects: 33.316041 ms  DeleteObjects: 5.689958 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  1.279 seconds
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.12 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.747 seconds
Domain Reload Profiling: 2029ms
	BeginReloadAssembly (464ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (97ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (113ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (741ms)
		LoadAssemblies (554ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (305ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (265ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (747ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (595ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (108ms)
			ProcessInitializeOnLoadAttributes (429ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 4.57 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.7 MB). Loaded Objects now: 6982.
Memory consumption went from 172.9 MB to 169.2 MB.
Total: 34.983250 ms (FindLiveObjects: 2.222875 ms CreateObjectMapping: 0.930667 ms MarkObjects: 23.968250 ms  DeleteObjects: 7.857833 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  1.506 seconds
Refreshing native plugins compatible for Editor in 1.90 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.29 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.708 seconds
Domain Reload Profiling: 2217ms
	BeginReloadAssembly (837ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (53ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (269ms)
	RebuildCommonClasses (48ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (568ms)
		LoadAssemblies (678ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (272ms)
			TypeCache.Refresh (54ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (205ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (708ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (558ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (77ms)
			ProcessInitializeOnLoadAttributes (435ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 1.81 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.7 MB). Loaded Objects now: 6984.
Memory consumption went from 172.9 MB to 169.2 MB.
Total: 8.775958 ms (FindLiveObjects: 0.622458 ms CreateObjectMapping: 0.201542 ms MarkObjects: 5.027417 ms  DeleteObjects: 2.924167 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.80 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6083 unused Assets / (6.2 MB). Loaded Objects now: 6984.
Memory consumption went from 163.1 MB to 156.9 MB.
Total: 91.120417 ms (FindLiveObjects: 1.239458 ms CreateObjectMapping: 0.410541 ms MarkObjects: 78.469000 ms  DeleteObjects: 11.000542 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  2.487 seconds
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.06 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.758 seconds
Domain Reload Profiling: 3249ms
	BeginReloadAssembly (1213ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (75ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (5ms)
		CreateAndSetChildDomain (461ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (21ms)
	initialDomainReloadingComplete (70ms)
	LoadAllAssembliesAndSetupDomain (1142ms)
		LoadAssemblies (1044ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (443ms)
			TypeCache.Refresh (141ms)
				TypeCache.ScanAssembly (9ms)
			BuildScriptInfoCaches (279ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (759ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (534ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (18ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (391ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.21 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.0 MB). Loaded Objects now: 6986.
Memory consumption went from 172.9 MB to 168.9 MB.
Total: 9.231666 ms (FindLiveObjects: 0.599708 ms CreateObjectMapping: 0.256459 ms MarkObjects: 5.435291 ms  DeleteObjects: 2.939666 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  1.576 seconds
Refreshing native plugins compatible for Editor in 2.38 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.16 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.653 seconds
Domain Reload Profiling: 2232ms
	BeginReloadAssembly (668ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (84ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (3ms)
		CreateAndSetChildDomain (201ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (821ms)
		LoadAssemblies (690ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (317ms)
			TypeCache.Refresh (37ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (259ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (653ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (488ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (359ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.4 MB). Loaded Objects now: 6988.
Memory consumption went from 172.8 MB to 168.4 MB.
Total: 10.563625 ms (FindLiveObjects: 1.584125 ms CreateObjectMapping: 0.291875 ms MarkObjects: 6.154458 ms  DeleteObjects: 2.532666 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  1.252 seconds
Refreshing native plugins compatible for Editor in 14.72 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.62 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.864 seconds
Domain Reload Profiling: 2119ms
	BeginReloadAssembly (597ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (40ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (3ms)
		CreateAndSetChildDomain (210ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (595ms)
		LoadAssemblies (523ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (211ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (170ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (864ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (666ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (95ms)
			ProcessInitializeOnLoadAttributes (506ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.52 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (3.7 MB). Loaded Objects now: 6990.
Memory consumption went from 172.9 MB to 169.2 MB.
Total: 21.837750 ms (FindLiveObjects: 1.205834 ms CreateObjectMapping: 0.545500 ms MarkObjects: 16.786791 ms  DeleteObjects: 3.298750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  1.794 seconds
Refreshing native plugins compatible for Editor in 6.34 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.00 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.737 seconds
Domain Reload Profiling: 2533ms
	BeginReloadAssembly (949ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (54ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (385ms)
	RebuildCommonClasses (61ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (737ms)
		LoadAssemblies (843ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (242ms)
			TypeCache.Refresh (29ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (196ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (737ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (542ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (118ms)
			ProcessInitializeOnLoadAttributes (376ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.12 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.3 MB). Loaded Objects now: 6992.
Memory consumption went from 172.9 MB to 168.5 MB.
Total: 7.934500 ms (FindLiveObjects: 0.427500 ms CreateObjectMapping: 0.283084 ms MarkObjects: 5.161208 ms  DeleteObjects: 2.062208 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.801 seconds
Refreshing native plugins compatible for Editor in 1.94 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.29 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.712 seconds
Domain Reload Profiling: 1517ms
	BeginReloadAssembly (321ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (142ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (415ms)
		LoadAssemblies (292ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (189ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (159ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (713ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (567ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (424ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (1ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.95 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6159 unused Assets / (4.8 MB). Loaded Objects now: 6994.
Memory consumption went from 172.9 MB to 168.0 MB.
Total: 10.062083 ms (FindLiveObjects: 0.345709 ms CreateObjectMapping: 0.328833 ms MarkObjects: 6.688083 ms  DeleteObjects: 2.698666 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  1.263 seconds
Refreshing native plugins compatible for Editor in 2.24 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 8.08 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.875 seconds
Domain Reload Profiling: 2145ms
	BeginReloadAssembly (482ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (34ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (155ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (707ms)
		LoadAssemblies (610ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (285ms)
			TypeCache.Refresh (29ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (225ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (876ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (663ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (116ms)
			ProcessInitializeOnLoadAttributes (491ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.99 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6160 unused Assets / (2.6 MB). Loaded Objects now: 6997.
Memory consumption went from 172.9 MB to 170.2 MB.
Total: 17.761125 ms (FindLiveObjects: 0.559959 ms CreateObjectMapping: 2.325791 ms MarkObjects: 10.877542 ms  DeleteObjects: 3.997250 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.922 seconds
Refreshing native plugins compatible for Editor in 3.64 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 5.10 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.782 seconds
Domain Reload Profiling: 1709ms
	BeginReloadAssembly (283ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (119ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (558ms)
		LoadAssemblies (325ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (311ms)
			TypeCache.Refresh (41ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (250ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (783ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (589ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (414ms)
			ProcessInitializeOnLoadMethodAttributes (79ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 2.10 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6160 unused Assets / (5.2 MB). Loaded Objects now: 6999.
Memory consumption went from 172.8 MB to 167.6 MB.
Total: 21.420000 ms (FindLiveObjects: 0.817958 ms CreateObjectMapping: 1.989458 ms MarkObjects: 15.828333 ms  DeleteObjects: 2.783584 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  1.114 seconds
Refreshing native plugins compatible for Editor in 2.01 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.97 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.669 seconds
Domain Reload Profiling: 1785ms
	BeginReloadAssembly (436ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (20ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (147ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (613ms)
		LoadAssemblies (526ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (277ms)
			TypeCache.Refresh (45ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (219ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (669ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (483ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (84ms)
			ProcessInitializeOnLoadAttributes (352ms)
			ProcessInitializeOnLoadMethodAttributes (35ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 1.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6160 unused Assets / (4.7 MB). Loaded Objects now: 7001.
Memory consumption went from 172.9 MB to 168.2 MB.
Total: 9.457417 ms (FindLiveObjects: 0.449042 ms CreateObjectMapping: 0.320375 ms MarkObjects: 6.341750 ms  DeleteObjects: 2.345792 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.904 seconds
Refreshing native plugins compatible for Editor in 2.49 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.61 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.881 seconds
Domain Reload Profiling: 1790ms
	BeginReloadAssembly (229ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (118ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (616ms)
		LoadAssemblies (294ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (366ms)
			TypeCache.Refresh (41ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (296ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (882ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (717ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (90ms)
			ProcessInitializeOnLoadAttributes (551ms)
			ProcessInitializeOnLoadMethodAttributes (67ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 2.90 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6160 unused Assets / (4.2 MB). Loaded Objects now: 7003.
Memory consumption went from 172.9 MB to 168.7 MB.
Total: 10.138333 ms (FindLiveObjects: 0.441708 ms CreateObjectMapping: 0.362042 ms MarkObjects: 6.706583 ms  DeleteObjects: 2.627333 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  1.120 seconds
Refreshing native plugins compatible for Editor in 3.24 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.95 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.665 seconds
Domain Reload Profiling: 1789ms
	BeginReloadAssembly (450ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (25ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (194ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (599ms)
		LoadAssemblies (500ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (226ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (184ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (666ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (469ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (84ms)
			ProcessInitializeOnLoadAttributes (344ms)
			ProcessInitializeOnLoadMethodAttributes (33ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.14 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6160 unused Assets / (3.5 MB). Loaded Objects now: 7005.
Memory consumption went from 172.9 MB to 169.4 MB.
Total: 11.545875 ms (FindLiveObjects: 0.725958 ms CreateObjectMapping: 0.765875 ms MarkObjects: 7.732000 ms  DeleteObjects: 2.321250 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.680 seconds
Refreshing native plugins compatible for Editor in 2.53 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.28 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.643 seconds
Domain Reload Profiling: 1326ms
	BeginReloadAssembly (259ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (134ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (362ms)
		LoadAssemblies (226ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (182ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (156ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (643ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (498ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (376ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.08 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6160 unused Assets / (5.7 MB). Loaded Objects now: 7007.
Memory consumption went from 172.9 MB to 167.2 MB.
Total: 9.630083 ms (FindLiveObjects: 0.438875 ms CreateObjectMapping: 0.280458 ms MarkObjects: 5.293583 ms  DeleteObjects: 3.616292 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  1.456 seconds
Refreshing native plugins compatible for Editor in 2.24 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.612 seconds
Domain Reload Profiling: 2072ms
	BeginReloadAssembly (721ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (96ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (4ms)
		CreateAndSetChildDomain (211ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (683ms)
		LoadAssemblies (660ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (239ms)
			TypeCache.Refresh (34ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (187ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (612ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (456ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (330ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6160 unused Assets / (4.0 MB). Loaded Objects now: 7009.
Memory consumption went from 172.8 MB to 168.8 MB.
Total: 9.310083 ms (FindLiveObjects: 2.082667 ms CreateObjectMapping: 0.200208 ms MarkObjects: 4.751125 ms  DeleteObjects: 2.275458 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.864 seconds
Refreshing native plugins compatible for Editor in 2.43 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.75 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.745 seconds
Domain Reload Profiling: 1612ms
	BeginReloadAssembly (340ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (183ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (452ms)
		LoadAssemblies (316ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (199ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (171ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (745ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (592ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (97ms)
			ProcessInitializeOnLoadAttributes (445ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6160 unused Assets / (4.9 MB). Loaded Objects now: 7011.
Memory consumption went from 172.9 MB to 168.0 MB.
Total: 15.215958 ms (FindLiveObjects: 0.432500 ms CreateObjectMapping: 0.360542 ms MarkObjects: 10.771542 ms  DeleteObjects: 3.650292 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  1.235 seconds
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.90 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.875 seconds
Domain Reload Profiling: 2119ms
	BeginReloadAssembly (450ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (228ms)
	RebuildCommonClasses (59ms)
	RebuildNativeTypeToScriptingClass (31ms)
	initialDomainReloadingComplete (67ms)
	LoadAllAssembliesAndSetupDomain (636ms)
		LoadAssemblies (507ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (269ms)
			TypeCache.Refresh (42ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (210ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (876ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (679ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (102ms)
			ProcessInitializeOnLoadAttributes (520ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6160 unused Assets / (3.1 MB). Loaded Objects now: 7013.
Memory consumption went from 172.9 MB to 169.8 MB.
Total: 12.600875 ms (FindLiveObjects: 1.500083 ms CreateObjectMapping: 0.522375 ms MarkObjects: 7.086500 ms  DeleteObjects: 3.490500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  1.656 seconds
Refreshing native plugins compatible for Editor in 2.53 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.20 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.758 seconds
Domain Reload Profiling: 2417ms
	BeginReloadAssembly (991ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (63ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (6ms)
		CreateAndSetChildDomain (330ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (599ms)
		LoadAssemblies (889ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (183ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (151ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (758ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (576ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (100ms)
			ProcessInitializeOnLoadAttributes (414ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6160 unused Assets / (2.9 MB). Loaded Objects now: 7015.
Memory consumption went from 172.9 MB to 170.0 MB.
Total: 18.539500 ms (FindLiveObjects: 0.481125 ms CreateObjectMapping: 0.356667 ms MarkObjects: 14.023125 ms  DeleteObjects: 3.677959 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.686 seconds
Refreshing native plugins compatible for Editor in 2.80 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.56 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.793 seconds
Domain Reload Profiling: 1482ms
	BeginReloadAssembly (251ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (127ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (376ms)
		LoadAssemblies (251ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (181ms)
			TypeCache.Refresh (27ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (142ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (793ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (562ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (118ms)
			ProcessInitializeOnLoadAttributes (384ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 3.08 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6160 unused Assets / (3.2 MB). Loaded Objects now: 7017.
Memory consumption went from 172.9 MB to 169.7 MB.
Total: 13.641666 ms (FindLiveObjects: 0.495833 ms CreateObjectMapping: 0.925709 ms MarkObjects: 9.948375 ms  DeleteObjects: 2.271458 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16af1f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.951 seconds
Refreshing native plugins compatible for Editor in 2.59 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.74 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.746 seconds
Domain Reload Profiling: 1702ms
	BeginReloadAssembly (491ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (20ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (346ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (399ms)
		LoadAssemblies (268ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (191ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (162ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (746ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (569ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (90ms)
			ProcessInitializeOnLoadAttributes (422ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.91 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6160 unused Assets / (2.5 MB). Loaded Objects now: 7019.
Memory consumption went from 172.8 MB to 170.4 MB.
Total: 15.115500 ms (FindLiveObjects: 0.628417 ms CreateObjectMapping: 0.498375 ms MarkObjects: 11.585500 ms  DeleteObjects: 2.401667 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportWorkerClient::OnTransportError - code=2 error=End of file
