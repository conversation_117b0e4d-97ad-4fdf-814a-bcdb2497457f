using FMODUnity;
using FMOD.Studio;
using System.Collections.Generic;
using UnityEngine;

public class LightningAudio : MonoBehaviour
{
    [Header("FMOD")] 
    [SerializeField] private EventReference violinNoteEvent;
    [SerializeField] private int maxSimultaneousNotes = 100;

    private ParticleSystem ps;
    private ParticleSystem.Particle[] particles;
    private List<EventInstance> eventPool = new List<EventInstance>();
    private Dictionary<int, int> particleToEvent = new Dictionary<int, int>(); // particleID -> eventPool index
    private HashSet<int> activeParticleIDs = new HashSet<int>();

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Awake()
    {
        ps = GetComponent<ParticleSystem>();
        particles = new ParticleSystem.Particle[ps.main.maxParticles];
        // Pre-create pool
        for (int i = 0; i < maxSimultaneousNotes; i++)
        {
            var inst = RuntimeManager.CreateInstance(violinNoteEvent);
            eventPool.Add(inst);
        }
    }

    void OnDestroy()
    {
        foreach (var inst in eventPool)
        {
            inst.stop(FMOD.Studio.STOP_MODE.IMMEDIATE);
            inst.release();
        }
    }

    // Update is called once per frame
    void Update()
    {
        int count = ps.GetParticles(particles);
        activeParticleIDs.Clear();
        // Track which particles are alive and need sound
        for (int i = 0; i < count; i++)
        {
            int id = (int)particles[i].randomSeed;
            activeParticleIDs.Add(id);
            if (!particleToEvent.ContainsKey(id))
            {
                // Find a free event instance
                int poolIdx = GetFreeEventIndex();
                if (poolIdx == -1) continue; // Pool exhausted
                var inst = eventPool[poolIdx];
                inst.set3DAttributes(RuntimeUtils.To3DAttributes(particles[i].position));
                inst.start();
                particleToEvent[id] = poolIdx;
            }
            else
            {
                // Update position
                int poolIdx = particleToEvent[id];
                eventPool[poolIdx].set3DAttributes(RuntimeUtils.To3DAttributes(particles[i].position));
            }
        }
        // Stop/release sounds for dead particles
        var toRemove = new List<int>();
        foreach (var kvp in particleToEvent)
        {
            if (!activeParticleIDs.Contains(kvp.Key))
            {
                var inst = eventPool[kvp.Value];
                inst.stop(FMOD.Studio.STOP_MODE.IMMEDIATE);
                // Don't release, keep for reuse
                toRemove.Add(kvp.Key);
            }
        }
        foreach (var id in toRemove)
            particleToEvent.Remove(id);
    }

    int GetFreeEventIndex()
    {
        for (int i = 0; i < eventPool.Count; i++)
        {
            bool inUse = false;
            foreach (var idx in particleToEvent.Values)
                if (idx == i) { inUse = true; break; }
            if (!inUse) return i;
        }
        return -1;
    }
}
