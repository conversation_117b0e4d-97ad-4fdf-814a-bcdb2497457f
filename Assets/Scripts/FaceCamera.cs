using UnityEngine;

public class FaceCamera : MonoBehaviour
{
    [SerializeField] private float speed = 2f;           // Speed of the movement
    [SerializeField] private float amplitude = 0.1f;     // Size of the movement (keep it small)

    private Vector3 startPosition;                       // Initial local position to orbit around
    private float timeOffset;                            // Random offset for unique movement timing
    private Transform cameraTransform;                        // Reference to the camera transform

    void Start()
    {
        // Store the initial local position as the center of our movement
        startPosition = transform.localPosition;
        // Add a random time offset so multiple instances don't sync perfectly
        timeOffset = Random.value * Mathf.PI * 2f;
        cameraTransform = GameManager.Instance.followCamera.transform;
    }

    void LateUpdate()
    {
        // Face the camera first
        transform.LookAt(cameraTransform);
        transform.forward = -cameraTransform.forward;

        // Calculate the infinity loop movement
        float time = Time.time * speed + timeOffset;

        // X movement (horizontal) using sine for smooth oscillation
        float x = Mathf.Sin(time) * amplitude;
        // Y movement (vertical) using sine with half frequency for ∞ shape
        float y = Mathf.Sin(time * 0.5f) * amplitude;

        // Create the offset vector in local space (relative to parent)
        Vector3 offset = new Vector3(
            x,
            y,
            0f  // No Z movement (forward/backward relative to camera)
        );

        // Apply the position relative to the starting point
        transform.localPosition = startPosition + offset;
    }
}