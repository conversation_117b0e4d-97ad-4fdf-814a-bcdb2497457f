using System.Collections.Generic;
using UnityEngine;
using FMODUnity;

/// <summary>
/// Manages a pool of BackpackParticles to improve performance when multiple SoulCreatures interact with the player.
/// </summary>
public class BackpackParticlesPool : MonoBehaviour
{
    public static BackpackParticlesPool Instance { get; private set; }

    [Header("Prefab References")]
    [SerializeField] private GameObject backpackParticlesPrefab;
    [SerializeField] private GameObject backpackParticlesHaloPrefab;

    [<PERSON><PERSON>("Material References")]
    [Tooltip("Material for regular soul creatures")]
    [SerializeField] private Material backpackParticlesMaterial;
    [Tooltip("Material for SoulCreatureGiant (bubbles)")]
    [SerializeField] private Material bubblesMaterial;

    [Header("Pool Settings")]
    [Tooltip("Initial size of the particle pool")]
    [SerializeField] private int initialPoolSize = 10;

    [Tooltip("Maximum number of active particle systems")]
    [SerializeField] private int maxActiveParticles = 5;

    [Tooltip("Maximum number of active halo particle systems")]
    [SerializeField] private int maxActiveHalos = 3;

    // Pools for both particle types
    private List<PooledParticleSystem> particlesPool = new List<PooledParticleSystem>();
    private List<PooledParticleSystem> halosPool = new List<PooledParticleSystem>();

    // Active particles tracking
    private int activeParticlesCount = 0;
    private int activeHalosCount = 0;

    // Class to track pooled particle systems
    private class PooledParticleSystem
    {
        public GameObject gameObject;
        public ParticleSystem particleSystem;
        public Color color;
        public EventReference soundEvent;
        public GameObject sourceCreature;
        public bool isActive = false;
        public float emissionRate = 0f;
        public float lastUsedTime = 0f;
    }

    private void Awake()
    {
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }

        Instance = this;

        // Initialize the pools
        InitializePool(particlesPool, backpackParticlesPrefab, initialPoolSize);
        InitializePool(halosPool, backpackParticlesHaloPrefab, initialPoolSize);
    }

    private void InitializePool(List<PooledParticleSystem> pool, GameObject prefab, int size)
    {
        if (prefab == null) return;

        for (int i = 0; i < size; i++)
        {
            GameObject obj = Instantiate(prefab, transform);
            obj.SetActive(false);

            PooledParticleSystem pooledSystem = new PooledParticleSystem
            {
                gameObject = obj,
                particleSystem = obj.GetComponent<ParticleSystem>(),
                isActive = false,
                lastUsedTime = 0f
            };

            pool.Add(pooledSystem);
        }
    }

    /// <summary>
    /// Get a backpack particle system from the pool.
    /// </summary>
    public GameObject GetBackpackParticles(Color color, EventReference soundEvent, GameObject sourceCreature)
    {
        // Check if we've reached the maximum active particles
        if (activeParticlesCount >= maxActiveParticles)
        {
            // Find the oldest active particle system and recycle it
            PooledParticleSystem oldest = FindOldestActive(particlesPool);
            if (oldest != null)
            {
                ConfigureParticleSystem(oldest, color, soundEvent, sourceCreature);
                return oldest.gameObject;
            }
            return null;
        }

        // Try to find an inactive particle system
        PooledParticleSystem available = FindInactive(particlesPool);

        // If none available, create a new one
        if (available == null)
        {
            GameObject obj = Instantiate(backpackParticlesPrefab, transform);
            available = new PooledParticleSystem
            {
                gameObject = obj,
                particleSystem = obj.GetComponent<ParticleSystem>(),
                isActive = false
            };
            particlesPool.Add(available);
        }

        // Configure and return the particle system
        ConfigureParticleSystem(available, color, soundEvent, sourceCreature);
        activeParticlesCount++;

        return available.gameObject;
    }

    /// <summary>
    /// Get a backpack halo particle system from the pool.
    /// </summary>
    public GameObject GetBackpackHalo(Color color, GameObject sourceCreature)
    {
        // Check if we've reached the maximum active halos
        if (activeHalosCount >= maxActiveHalos)
        {
            // Find the oldest active halo and recycle it
            PooledParticleSystem oldest = FindOldestActive(halosPool);
            if (oldest != null)
            {
                ConfigureHaloSystem(oldest, color, sourceCreature);
                return oldest.gameObject;
            }
            return null;
        }

        // Try to find an inactive halo
        PooledParticleSystem available = FindInactive(halosPool);

        // If none available, create a new one
        if (available == null)
        {
            GameObject obj = Instantiate(backpackParticlesHaloPrefab, transform);
            available = new PooledParticleSystem
            {
                gameObject = obj,
                particleSystem = obj.GetComponent<ParticleSystem>(),
                isActive = false
            };
            halosPool.Add(available);
        }

        // Configure and return the halo
        ConfigureHaloSystem(available, color, sourceCreature);
        activeHalosCount++;

        return available.gameObject;
    }

    private void ConfigureParticleSystem(PooledParticleSystem pooled, Color color, EventReference soundEvent, GameObject sourceCreature)
    {
        pooled.gameObject.SetActive(true);
        pooled.isActive = true;
        pooled.color = color;
        pooled.soundEvent = soundEvent;
        pooled.sourceCreature = sourceCreature;
        pooled.lastUsedTime = Time.time;

        // Set the particle color and size
        var main = pooled.particleSystem.main;
        main.startColor = color;

        // Check creature type and configure accordingly
        bool isFromSnakeRiver = sourceCreature != null && sourceCreature.GetComponent<ParticleSnakeRiverController>() != null;
        bool isFromSoulCreatureGiant = sourceCreature != null && sourceCreature.GetComponent<SoulCreatureGiant>() != null;

        // Configure particle size and material based on source creature type
        if (isFromSnakeRiver)
        {
            main.startSize = 0.25f;
            SetParticleMaterial(pooled.particleSystem, backpackParticlesMaterial);
        }
        else if (isFromSoulCreatureGiant)
        {
            main.startSize = 0.2f; // Smaller size for SoulCreatureGiant
            SetParticleMaterial(pooled.particleSystem, bubblesMaterial);
        }
        else
        {
            main.startSize = 0.4f; // Default size for regular soul creatures
            SetParticleMaterial(pooled.particleSystem, backpackParticlesMaterial);
        }

        // Set the sound event
        var soundComponent = pooled.gameObject.GetComponent<BackpackParticlesSoundOnDeath>();
        if (soundComponent == null)
        {
            soundComponent = pooled.gameObject.AddComponent<BackpackParticlesSoundOnDeath>();
        }

        // Make sure the sound event is set properly
        if (!soundEvent.IsNull)
        {
            soundComponent.sound2 = soundEvent;
            // Path property is only available in editor
            // Debug.Log($"Set sound event on BackpackParticles: {soundEvent.Path}");
        }
        else if (sourceCreature != null)
        {
            // Try to get the sound event from the source creature
            var scAudio = sourceCreature.GetComponent<SoulCreatureAudio>();
            if (scAudio != null && !scAudio.Sound2.IsNull)
            {
                soundComponent.sound2 = scAudio.Sound2;
#if UNITY_EDITOR
                Debug.Log($"Set sound event from SoulCreatureAudio: {scAudio.Sound2.Path}");
#else
                Debug.Log("Set sound event from SoulCreatureAudio");
#endif
            }
            else
            {
                var scTutorial = sourceCreature.GetComponent<SoulCreatureTutorial>();
                if (scTutorial != null && !scTutorial.sound2.IsNull)
                {
                    soundComponent.sound2 = scTutorial.sound2;
#if UNITY_EDITOR
                    Debug.Log($"Set sound event from SoulCreatureTutorial: {scTutorial.sound2.Path}");
#else
                    Debug.Log("Set sound event from SoulCreatureTutorial");
#endif
                }
            }
        }

        // Reset emission rate
        var emission = pooled.particleSystem.emission;
        emission.rateOverTime = 0f;
        pooled.emissionRate = 0f;
    }

    private void ConfigureHaloSystem(PooledParticleSystem pooled, Color color, GameObject sourceCreature)
    {
        pooled.gameObject.SetActive(true);
        pooled.isActive = true;
        pooled.color = color;
        pooled.sourceCreature = sourceCreature;
        pooled.lastUsedTime = Time.time;

        // Set the particle color and size
        var main = pooled.particleSystem.main;
        main.startColor = color;

        // Check creature type and configure accordingly
        bool isFromSnakeRiver = sourceCreature != null && sourceCreature.GetComponent<ParticleSnakeRiverController>() != null;
        bool isFromSoulCreatureGiant = sourceCreature != null && sourceCreature.GetComponent<SoulCreatureGiant>() != null;

        // Configure particle size and material based on source creature type
        if (isFromSnakeRiver)
        {
            main.startSize = 0.25f;
            SetParticleMaterial(pooled.particleSystem, backpackParticlesMaterial);
        }
        else if (isFromSoulCreatureGiant)
        {
            main.startSize = 0.2f; // Smaller size for SoulCreatureGiant
            SetParticleMaterial(pooled.particleSystem, bubblesMaterial);
        }
        else
        {
            main.startSize = 0.4f; // Default size for regular soul creatures
            SetParticleMaterial(pooled.particleSystem, backpackParticlesMaterial);
        }

        // Reset emission rate
        var emission = pooled.particleSystem.emission;
        emission.rateOverTime = 0f;
        pooled.emissionRate = 0f;
    }

    /// <summary>
    /// Update the emission rate of a particle system.
    /// </summary>
    public void UpdateEmissionRate(GameObject particleObj, float emissionRate)
    {
        // Find the particle system in either pool
        PooledParticleSystem pooled = FindByGameObject(particlesPool, particleObj);
        if (pooled == null)
        {
            pooled = FindByGameObject(halosPool, particleObj);
        }

        if (pooled != null)
        {
            var emission = pooled.particleSystem.emission;
            emission.rateOverTime = emissionRate;
            pooled.emissionRate = emissionRate;
            pooled.lastUsedTime = Time.time;

            // Ensure the particle size and material are correct based on the source creature
            var main = pooled.particleSystem.main;
            bool isFromSnakeRiver = pooled.sourceCreature != null && pooled.sourceCreature.GetComponent<ParticleSnakeRiverController>() != null;
            bool isFromSoulCreatureGiant = pooled.sourceCreature != null && pooled.sourceCreature.GetComponent<SoulCreatureGiant>() != null;

            if (isFromSnakeRiver)
            {
                main.startSize = 0.25f;
                SetParticleMaterial(pooled.particleSystem, backpackParticlesMaterial);
            }
            else if (isFromSoulCreatureGiant)
            {
                main.startSize = 0.2f;
                SetParticleMaterial(pooled.particleSystem, bubblesMaterial);
            }
            else
            {
                main.startSize = 0.4f; // Default size for regular soul creatures
                SetParticleMaterial(pooled.particleSystem, backpackParticlesMaterial);
            }
        }
    }

    /// <summary>
    /// Release a particle system back to the pool.
    /// </summary>
    public void ReleaseParticleSystem(GameObject particleObj)
    {
        // Check if it's a regular particle system
        PooledParticleSystem pooled = FindByGameObject(particlesPool, particleObj);
        if (pooled != null)
        {
            pooled.isActive = false;
            pooled.gameObject.SetActive(false);
            activeParticlesCount--;
            return;
        }

        // Check if it's a halo
        pooled = FindByGameObject(halosPool, particleObj);
        if (pooled != null)
        {
            pooled.isActive = false;
            pooled.gameObject.SetActive(false);
            activeHalosCount--;
        }
    }

    private PooledParticleSystem FindInactive(List<PooledParticleSystem> pool)
    {
        foreach (var system in pool)
        {
            if (!system.isActive)
            {
                return system;
            }
        }
        return null;
    }

    private PooledParticleSystem FindOldestActive(List<PooledParticleSystem> pool)
    {
        PooledParticleSystem oldest = null;
        float oldestTime = float.MaxValue;

        foreach (var system in pool)
        {
            if (system.isActive && system.lastUsedTime < oldestTime)
            {
                oldest = system;
                oldestTime = system.lastUsedTime;
            }
        }

        return oldest;
    }

    private PooledParticleSystem FindByGameObject(List<PooledParticleSystem> pool, GameObject obj)
    {
        foreach (var system in pool)
        {
            if (system.gameObject == obj)
            {
                return system;
            }
        }
        return null;
    }

    /// <summary>
    /// Helper method to set the material of a particle system.
    /// </summary>
    private void SetParticleMaterial(ParticleSystem ps, Material material)
    {
        if (ps == null || material == null) return;

        var renderer = ps.GetComponent<ParticleSystemRenderer>();
        if (renderer != null)
        {
            renderer.material = material;
        }
    }
}
