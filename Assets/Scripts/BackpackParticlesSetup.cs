using UnityEngine;
using FMODUnity;

/// <summary>
/// Helper script to set up the BackpackParticlesPool on the player.
/// This script should be attached to the player GameObject.
/// </summary>
public class BackpackParticlesSetup : MonoBehaviour
{
    [Header("Prefab References")]
    [Tooltip("Prefab for regular backpack particles")]
    [SerializeField] private GameObject backpackParticlesPrefab;

    [<PERSON><PERSON><PERSON>("Prefab for halo backpack particles")]
    [SerializeField] private GameObject backpackParticlesHaloPrefab;

    [<PERSON><PERSON>("Material References")]
    [Tooltip("Material for regular soul creatures")]
    [SerializeField] private Material backpackParticlesMaterial;

    [Tooltip("Material for SoulCreatureGiant (bubbles)")]
    [SerializeField] private Material bubblesMaterial;
    
    [Header("Pool Settings")]
    [Tooltip("Initial size of the particle pool")]
    [SerializeField] private int initialPoolSize = 10;
    
    [Tooltip("Maximum number of active particle systems")]
    [SerializeField] private int maxActiveParticles = 5;
    
    [<PERSON>lt<PERSON>("Maximum number of active halo particle systems")]
    [SerializeField] private int maxActiveHalos = 3;
    
    private void Awake()
    {
        // Check if the player already has a BackpackParticlesPool component
        BackpackParticlesPool existingPool = GetComponent<BackpackParticlesPool>();
        if (existingPool != null)
        {
            Debug.Log("BackpackParticlesPool already exists on player. Using existing pool.");
            return;
        }
        
        // Add the BackpackParticlesPool component to the player
        BackpackParticlesPool pool = gameObject.AddComponent<BackpackParticlesPool>();
        
        // Set up the pool with the prefabs and settings
        SetupPool(pool);
        
        // Get the PlayerController component
        PlayerController playerController = GetComponent<PlayerController>();
        if (playerController != null)
        {
            // Set the particlesPool field in PlayerController using reflection
            System.Reflection.FieldInfo fieldInfo = typeof(PlayerController).GetField("particlesPool", 
                System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
            
            if (fieldInfo != null)
            {
                fieldInfo.SetValue(playerController, pool);
//                Debug.Log("Successfully set particlesPool reference in PlayerController.");
            }
            else
            {
                Debug.LogError("Could not find particlesPool field in PlayerController.");
            }
        }
        else
        {
            Debug.LogError("PlayerController component not found on player GameObject.");
        }
    }
    
    private void SetupPool(BackpackParticlesPool pool)
    {
        // Set the prefab and material references using reflection
        System.Reflection.FieldInfo backpackParticlesPrefabField = typeof(BackpackParticlesPool).GetField("backpackParticlesPrefab",
            System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);

        System.Reflection.FieldInfo backpackParticlesHaloPrefabField = typeof(BackpackParticlesPool).GetField("backpackParticlesHaloPrefab",
            System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);

        System.Reflection.FieldInfo backpackParticlesMaterialField = typeof(BackpackParticlesPool).GetField("backpackParticlesMaterial",
            System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);

        System.Reflection.FieldInfo bubblesMaterialField = typeof(BackpackParticlesPool).GetField("bubblesMaterial",
            System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);

        System.Reflection.FieldInfo initialPoolSizeField = typeof(BackpackParticlesPool).GetField("initialPoolSize",
            System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);

        System.Reflection.FieldInfo maxActiveParticlesField = typeof(BackpackParticlesPool).GetField("maxActiveParticles",
            System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);

        System.Reflection.FieldInfo maxActiveHalosField = typeof(BackpackParticlesPool).GetField("maxActiveHalos",
            System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
        
        if (backpackParticlesPrefabField != null)
        {
            backpackParticlesPrefabField.SetValue(pool, backpackParticlesPrefab);
        }

        if (backpackParticlesHaloPrefabField != null)
        {
            backpackParticlesHaloPrefabField.SetValue(pool, backpackParticlesHaloPrefab);
        }

        if (backpackParticlesMaterialField != null)
        {
            backpackParticlesMaterialField.SetValue(pool, backpackParticlesMaterial);
        }

        if (bubblesMaterialField != null)
        {
            bubblesMaterialField.SetValue(pool, bubblesMaterial);
        }
        
        if (initialPoolSizeField != null)
        {
            initialPoolSizeField.SetValue(pool, initialPoolSize);
        }
        
        if (maxActiveParticlesField != null)
        {
            maxActiveParticlesField.SetValue(pool, maxActiveParticles);
        }
        
        if (maxActiveHalosField != null)
        {
            maxActiveHalosField.SetValue(pool, maxActiveHalos);
        }
        
        // Call the Awake method manually to initialize the pool
        System.Reflection.MethodInfo awakeMethod = typeof(BackpackParticlesPool).GetMethod("Awake", 
            System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
        
        if (awakeMethod != null)
        {
            awakeMethod.Invoke(pool, null);
        }
    }
}
