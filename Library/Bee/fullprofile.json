{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 23505, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 23505, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 23505, "tid": 13, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 23505, "tid": 13, "ts": 1748960924292364, "dur": 626, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 23505, "tid": 13, "ts": 1748960924295667, "dur": 612, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 23505, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 23505, "tid": 1, "ts": 1748960922030689, "dur": 9272, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 23505, "tid": 1, "ts": 1748960922039966, "dur": 47259, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 23505, "tid": 1, "ts": 1748960922087235, "dur": 43168, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 23505, "tid": 13, "ts": 1748960924296283, "dur": 197, "ph": "X", "name": "", "args": {}}, {"pid": 23505, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922027245, "dur": 27211, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922054463, "dur": 2230999, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922055935, "dur": 11921, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922067959, "dur": 1648, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922069614, "dur": 15139, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922084760, "dur": 587, "ph": "X", "name": "ProcessMessages 8186", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922085350, "dur": 48, "ph": "X", "name": "ReadAsync 8186", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922085400, "dur": 3, "ph": "X", "name": "ProcessMessages 8140", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922085404, "dur": 148, "ph": "X", "name": "ReadAsync 8140", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922085558, "dur": 3, "ph": "X", "name": "ProcessMessages 1097", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922085564, "dur": 63, "ph": "X", "name": "ReadAsync 1097", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922085630, "dur": 2, "ph": "X", "name": "ProcessMessages 894", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922085634, "dur": 57, "ph": "X", "name": "ReadAsync 894", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922085694, "dur": 2, "ph": "X", "name": "ProcessMessages 885", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922085698, "dur": 57, "ph": "X", "name": "ReadAsync 885", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922085757, "dur": 1, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922085760, "dur": 116, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922085879, "dur": 2, "ph": "X", "name": "ProcessMessages 900", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922085883, "dur": 44, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922085929, "dur": 1, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922085932, "dur": 203, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922086137, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922086140, "dur": 46, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922086188, "dur": 4, "ph": "X", "name": "ProcessMessages 2658", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922086194, "dur": 42, "ph": "X", "name": "ReadAsync 2658", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922086238, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922086240, "dur": 85, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922086328, "dur": 1, "ph": "X", "name": "ProcessMessages 910", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922086331, "dur": 320, "ph": "X", "name": "ReadAsync 910", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922086654, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922086657, "dur": 813, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922087473, "dur": 4, "ph": "X", "name": "ProcessMessages 8191", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922087478, "dur": 68, "ph": "X", "name": "ReadAsync 8191", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922087549, "dur": 128, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922087682, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922087684, "dur": 64, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922087756, "dur": 4, "ph": "X", "name": "ProcessMessages 1783", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922087761, "dur": 63, "ph": "X", "name": "ReadAsync 1783", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922087826, "dur": 3, "ph": "X", "name": "ProcessMessages 1849", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922087831, "dur": 60, "ph": "X", "name": "ReadAsync 1849", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922087894, "dur": 2, "ph": "X", "name": "ProcessMessages 940", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922087897, "dur": 167, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922088066, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922088069, "dur": 1969, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922090042, "dur": 6, "ph": "X", "name": "ProcessMessages 8185", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922090049, "dur": 92, "ph": "X", "name": "ReadAsync 8185", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922090143, "dur": 1, "ph": "X", "name": "ProcessMessages 961", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922090145, "dur": 40, "ph": "X", "name": "ReadAsync 961", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922090206, "dur": 146, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922090355, "dur": 20, "ph": "X", "name": "ReadAsync 1090", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922090378, "dur": 60, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922090444, "dur": 3, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922090449, "dur": 68, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922090521, "dur": 4, "ph": "X", "name": "ProcessMessages 1333", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922090536, "dur": 63, "ph": "X", "name": "ReadAsync 1333", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922090602, "dur": 1, "ph": "X", "name": "ProcessMessages 2073", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922090604, "dur": 27, "ph": "X", "name": "ReadAsync 2073", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922090633, "dur": 53, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922090689, "dur": 257, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922090947, "dur": 1, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922090948, "dur": 23, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922090974, "dur": 84, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922091060, "dur": 37, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922091097, "dur": 1, "ph": "X", "name": "ProcessMessages 990", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922091099, "dur": 38, "ph": "X", "name": "ReadAsync 990", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922091139, "dur": 90, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922091231, "dur": 2, "ph": "X", "name": "ProcessMessages 1222", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922091235, "dur": 60, "ph": "X", "name": "ReadAsync 1222", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922091296, "dur": 1, "ph": "X", "name": "ProcessMessages 958", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922091298, "dur": 271, "ph": "X", "name": "ReadAsync 958", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922091570, "dur": 1, "ph": "X", "name": "ProcessMessages 1750", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922091572, "dur": 105, "ph": "X", "name": "ReadAsync 1750", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922091679, "dur": 146, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922091827, "dur": 1, "ph": "X", "name": "ProcessMessages 1304", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922091829, "dur": 46, "ph": "X", "name": "ReadAsync 1304", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922091875, "dur": 2, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922091878, "dur": 124, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922092004, "dur": 60, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922092066, "dur": 35, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922092103, "dur": 19, "ph": "X", "name": "ReadAsync 1111", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922092125, "dur": 52, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922092179, "dur": 33, "ph": "X", "name": "ReadAsync 1161", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922092215, "dur": 20, "ph": "X", "name": "ReadAsync 833", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922092237, "dur": 23, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922092263, "dur": 40, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922092304, "dur": 38, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922092343, "dur": 2, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922092346, "dur": 48, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922092396, "dur": 170, "ph": "X", "name": "ReadAsync 1407", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922092568, "dur": 1, "ph": "X", "name": "ProcessMessages 1062", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922092570, "dur": 15, "ph": "X", "name": "ReadAsync 1062", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922092588, "dur": 18, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922092608, "dur": 36, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922092683, "dur": 17, "ph": "X", "name": "ReadAsync 937", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922092701, "dur": 1, "ph": "X", "name": "ProcessMessages 1283", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922092702, "dur": 602, "ph": "X", "name": "ReadAsync 1283", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922093306, "dur": 1, "ph": "X", "name": "ProcessMessages 1907", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922093308, "dur": 41, "ph": "X", "name": "ReadAsync 1907", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922093351, "dur": 44, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922093396, "dur": 1, "ph": "X", "name": "ProcessMessages 876", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922093397, "dur": 33, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922093434, "dur": 740, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922094176, "dur": 3, "ph": "X", "name": "ProcessMessages 8190", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922094180, "dur": 43, "ph": "X", "name": "ReadAsync 8190", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922094225, "dur": 74, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922094301, "dur": 1, "ph": "X", "name": "ProcessMessages 1668", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922094303, "dur": 56, "ph": "X", "name": "ReadAsync 1668", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922094360, "dur": 1, "ph": "X", "name": "ProcessMessages 1188", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922094363, "dur": 512, "ph": "X", "name": "ReadAsync 1188", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922094877, "dur": 23, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922094903, "dur": 64, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922094970, "dur": 2, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922094973, "dur": 43, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922095018, "dur": 1, "ph": "X", "name": "ProcessMessages 2134", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922095020, "dur": 147, "ph": "X", "name": "ReadAsync 2134", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922095170, "dur": 4, "ph": "X", "name": "ProcessMessages 1847", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922095176, "dur": 39, "ph": "X", "name": "ReadAsync 1847", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922095218, "dur": 1, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922095221, "dur": 119, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922095342, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922095344, "dur": 428, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922095774, "dur": 2, "ph": "X", "name": "ProcessMessages 924", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922095778, "dur": 64, "ph": "X", "name": "ReadAsync 924", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922095845, "dur": 30, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922095876, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922095878, "dur": 85, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922095978, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922095981, "dur": 92, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922096079, "dur": 1, "ph": "X", "name": "ProcessMessages 162", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922096081, "dur": 311, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922096395, "dur": 1, "ph": "X", "name": "ProcessMessages 2030", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922096397, "dur": 340, "ph": "X", "name": "ReadAsync 2030", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922096738, "dur": 1, "ph": "X", "name": "ProcessMessages 761", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922096739, "dur": 34, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922096775, "dur": 1, "ph": "X", "name": "ProcessMessages 1549", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922096777, "dur": 282, "ph": "X", "name": "ReadAsync 1549", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097060, "dur": 2, "ph": "X", "name": "ProcessMessages 2877", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097063, "dur": 40, "ph": "X", "name": "ReadAsync 2877", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097105, "dur": 45, "ph": "X", "name": "ReadAsync 1023", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097152, "dur": 23, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097178, "dur": 18, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097198, "dur": 36, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097263, "dur": 37, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097301, "dur": 1, "ph": "X", "name": "ProcessMessages 1491", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097302, "dur": 40, "ph": "X", "name": "ReadAsync 1491", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097346, "dur": 47, "ph": "X", "name": "ReadAsync 1347", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097395, "dur": 19, "ph": "X", "name": "ReadAsync 1187", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097416, "dur": 17, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097435, "dur": 25, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097462, "dur": 22, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097487, "dur": 56, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097544, "dur": 16, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097562, "dur": 20, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097584, "dur": 51, "ph": "X", "name": "ReadAsync 779", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097637, "dur": 19, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097658, "dur": 119, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097780, "dur": 18, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097799, "dur": 1, "ph": "X", "name": "ProcessMessages 2339", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097802, "dur": 22, "ph": "X", "name": "ReadAsync 2339", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097826, "dur": 49, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097878, "dur": 15, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097896, "dur": 17, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097915, "dur": 16, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097934, "dur": 15, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097951, "dur": 16, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097982, "dur": 16, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922097999, "dur": 19, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922098020, "dur": 25, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922098047, "dur": 70, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922098120, "dur": 44, "ph": "X", "name": "ReadAsync 1052", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922098166, "dur": 40, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922098207, "dur": 1, "ph": "X", "name": "ProcessMessages 2028", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922098209, "dur": 24, "ph": "X", "name": "ReadAsync 2028", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922098235, "dur": 41, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922098278, "dur": 81, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922098360, "dur": 30, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922098399, "dur": 1, "ph": "X", "name": "ProcessMessages 2138", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922098401, "dur": 37, "ph": "X", "name": "ReadAsync 2138", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922099121, "dur": 1, "ph": "X", "name": "ProcessMessages 1350", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922099124, "dur": 25, "ph": "X", "name": "ReadAsync 1350", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922099150, "dur": 3, "ph": "X", "name": "ProcessMessages 8148", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922099154, "dur": 19, "ph": "X", "name": "ReadAsync 8148", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922099176, "dur": 66, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922099257, "dur": 1, "ph": "X", "name": "ProcessMessages 1303", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922099259, "dur": 188, "ph": "X", "name": "ReadAsync 1303", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922099448, "dur": 286, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922099736, "dur": 1, "ph": "X", "name": "ProcessMessages 1737", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922099737, "dur": 36, "ph": "X", "name": "ReadAsync 1737", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922099775, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922099776, "dur": 42, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922099820, "dur": 31, "ph": "X", "name": "ReadAsync 1033", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922099854, "dur": 100, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922099958, "dur": 5, "ph": "X", "name": "ProcessMessages 1712", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922099964, "dur": 47, "ph": "X", "name": "ReadAsync 1712", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922100016, "dur": 2, "ph": "X", "name": "ProcessMessages 1227", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922100020, "dur": 51, "ph": "X", "name": "ReadAsync 1227", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922100072, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922100074, "dur": 327, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922100403, "dur": 494, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922100900, "dur": 394, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922101296, "dur": 1119, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922102417, "dur": 2, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922102419, "dur": 48, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922102471, "dur": 3367, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922105841, "dur": 1, "ph": "X", "name": "ProcessMessages 1132", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922105844, "dur": 275, "ph": "X", "name": "ReadAsync 1132", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922106121, "dur": 1106, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922107229, "dur": 1, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922107231, "dur": 1089, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922108321, "dur": 1, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922108322, "dur": 4143, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922112494, "dur": 23, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922112528, "dur": 1210, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922113739, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922113741, "dur": 1340, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922115085, "dur": 1056, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922116145, "dur": 1, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922116147, "dur": 2730, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922118889, "dur": 5, "ph": "X", "name": "ProcessMessages 1370", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922118897, "dur": 512, "ph": "X", "name": "ReadAsync 1370", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922119413, "dur": 2, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922119416, "dur": 670, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922120091, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922120093, "dur": 529, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922120626, "dur": 3, "ph": "X", "name": "ProcessMessages 918", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922120630, "dur": 1130, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922121766, "dur": 3, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922121771, "dur": 733, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922122526, "dur": 2, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922122529, "dur": 4389, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922126931, "dur": 11, "ph": "X", "name": "ProcessMessages 3792", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922126944, "dur": 567, "ph": "X", "name": "ReadAsync 3792", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922127515, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922127518, "dur": 530, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922128078, "dur": 5, "ph": "X", "name": "ProcessMessages 2051", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922128086, "dur": 899, "ph": "X", "name": "ReadAsync 2051", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922128989, "dur": 2, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922128992, "dur": 517, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922129514, "dur": 2, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922129518, "dur": 91, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922129612, "dur": 40, "ph": "X", "name": "ProcessMessages 2219", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922129656, "dur": 1254, "ph": "X", "name": "ReadAsync 2219", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922130924, "dur": 1, "ph": "X", "name": "ProcessMessages 813", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922130926, "dur": 437, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922131366, "dur": 5, "ph": "X", "name": "ProcessMessages 2700", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922131372, "dur": 694, "ph": "X", "name": "ReadAsync 2700", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922132069, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922132071, "dur": 133, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922132225, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922132228, "dur": 553, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922132783, "dur": 1, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922132785, "dur": 720, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922133507, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922133509, "dur": 265, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922133776, "dur": 2, "ph": "X", "name": "ProcessMessages 2198", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922133779, "dur": 940, "ph": "X", "name": "ReadAsync 2198", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922134721, "dur": 1, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922134722, "dur": 548, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922135273, "dur": 133, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922135409, "dur": 1263, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922136679, "dur": 3, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922136683, "dur": 177, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922136863, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922136865, "dur": 1303, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922138171, "dur": 2, "ph": "X", "name": "ProcessMessages 983", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922138174, "dur": 142, "ph": "X", "name": "ReadAsync 983", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922138318, "dur": 1734, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922140056, "dur": 1, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922140058, "dur": 212, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922140276, "dur": 524, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922140802, "dur": 82, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922140888, "dur": 245, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922141135, "dur": 113, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922141251, "dur": 174, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922141427, "dur": 290, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922141721, "dur": 209, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922141932, "dur": 205, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922142155, "dur": 159, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922142317, "dur": 172, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922142490, "dur": 1, "ph": "X", "name": "ProcessMessages 936", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922142491, "dur": 696, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922143190, "dur": 717, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922143910, "dur": 1150, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922145070, "dur": 4, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922145076, "dur": 321, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922145403, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922145405, "dur": 1163, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922146570, "dur": 1, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922146572, "dur": 1189, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922147764, "dur": 665, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922148431, "dur": 87, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922148520, "dur": 599, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922149123, "dur": 614, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922149739, "dur": 694, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922150436, "dur": 591, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922151029, "dur": 650, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922151682, "dur": 632, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922152317, "dur": 69, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922152388, "dur": 696, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922153087, "dur": 22, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922153112, "dur": 31, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922153145, "dur": 493, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922153640, "dur": 846, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922154491, "dur": 580, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922155074, "dur": 650, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922155731, "dur": 3, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922155782, "dur": 412, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922156197, "dur": 11, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922156211, "dur": 120, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922156333, "dur": 1, "ph": "X", "name": "ProcessMessages 861", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922156334, "dur": 219, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922156556, "dur": 5, "ph": "X", "name": "ProcessMessages 2352", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922156563, "dur": 81, "ph": "X", "name": "ReadAsync 2352", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922156648, "dur": 205, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922156858, "dur": 25, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922156885, "dur": 25, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922156912, "dur": 30, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922156944, "dur": 651, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922157600, "dur": 185, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922157787, "dur": 524, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922158313, "dur": 199, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922158514, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922158515, "dur": 14, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922158531, "dur": 16, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922158549, "dur": 17, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922158567, "dur": 27, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922158596, "dur": 1018, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922159617, "dur": 15, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922159634, "dur": 14, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922159649, "dur": 18, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922159670, "dur": 29, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922159701, "dur": 628, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922160331, "dur": 175, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922160508, "dur": 13, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922160523, "dur": 16, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922160541, "dur": 19, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922160562, "dur": 28, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922160592, "dur": 945, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922161540, "dur": 161, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922161702, "dur": 14, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922161718, "dur": 15, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922161736, "dur": 33, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922161771, "dur": 943, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922162720, "dur": 2, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922162724, "dur": 321, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922163048, "dur": 738, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922163790, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922163892, "dur": 3, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922163897, "dur": 70, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922163971, "dur": 163, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922164136, "dur": 10, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922164150, "dur": 215, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922164365, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922164368, "dur": 162, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922164532, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922164536, "dur": 121, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922164659, "dur": 76, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922164737, "dur": 195, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922164934, "dur": 248, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922165186, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922165190, "dur": 171, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922165377, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922165476, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922165479, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922165537, "dur": 233, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922165771, "dur": 154, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922165929, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922165932, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922165973, "dur": 222, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922166201, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922166203, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922166280, "dur": 109, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922166391, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922166393, "dur": 175, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922166570, "dur": 110, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922166682, "dur": 157, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922166843, "dur": 150, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922166993, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922166996, "dur": 160, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922167158, "dur": 58, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922167218, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922167296, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922167382, "dur": 70, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922167454, "dur": 92, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922167547, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922167550, "dur": 136, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922167688, "dur": 60, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922167750, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922167824, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922167845, "dur": 122, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922167971, "dur": 10, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922167983, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922168044, "dur": 160, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922168207, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922168210, "dur": 70, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922168283, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922168286, "dur": 64, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922168352, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922168377, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922168478, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922168481, "dur": 106, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922168589, "dur": 113, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922168706, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922168728, "dur": 145, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922168875, "dur": 122, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922169000, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922169003, "dur": 46, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922169052, "dur": 73, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922169126, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922169128, "dur": 66, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922169196, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922169244, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922169301, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922169377, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922169394, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922169454, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922169498, "dur": 67, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922169567, "dur": 270, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922169838, "dur": 103, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922169944, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922169947, "dur": 59, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922170008, "dur": 72, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922170082, "dur": 80, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922170164, "dur": 97, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922170263, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922170287, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922170336, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922170385, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922170424, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922170465, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922170523, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922170571, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922170593, "dur": 297, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922170893, "dur": 16, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922170911, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922170998, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922171062, "dur": 284, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922171348, "dur": 103, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922171451, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922171453, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922171498, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922171523, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922171600, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922171618, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922171643, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922171688, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922171709, "dur": 61, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922171771, "dur": 39, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922171812, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922171847, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922171869, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922171919, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922171939, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922171958, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922172025, "dur": 21, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922172048, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922172073, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922172157, "dur": 61, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922172219, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922172221, "dur": 104, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922172328, "dur": 22, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922172351, "dur": 79, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922172433, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922172485, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922172556, "dur": 80, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922172638, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922172755, "dur": 41, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922172798, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922172814, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922172833, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922172835, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922172868, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922172911, "dur": 60, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922172972, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922172995, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922173056, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922173077, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922173095, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922173135, "dur": 95, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922173232, "dur": 68, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922173302, "dur": 52, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922173358, "dur": 60, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922173419, "dur": 97, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922173518, "dur": 68, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922173588, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922173629, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922173664, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922173684, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922173724, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922173726, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922173764, "dur": 87, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922173853, "dur": 157, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922174012, "dur": 16733, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922190754, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922190757, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922190836, "dur": 362, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922191201, "dur": 166, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922191383, "dur": 13, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922191398, "dur": 3253, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922194657, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922194660, "dur": 6553, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922201217, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922201323, "dur": 848, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922202174, "dur": 119, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922202295, "dur": 311, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922202608, "dur": 566, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922203177, "dur": 179, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922203358, "dur": 127, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922203488, "dur": 909, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922204398, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922204401, "dur": 493, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922204896, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922204982, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922205083, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922205200, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922205325, "dur": 1728, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922207057, "dur": 108, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922207166, "dur": 118, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922207286, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922207380, "dur": 602, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922207984, "dur": 139, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922208125, "dur": 766, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922208893, "dur": 1249, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922210145, "dur": 273, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922210420, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922210519, "dur": 324, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922210846, "dur": 149, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922210996, "dur": 1953, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922212953, "dur": 131, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922213090, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922213094, "dur": 447, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922213544, "dur": 118, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922213664, "dur": 178, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922213844, "dur": 294, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922214140, "dur": 172, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922214314, "dur": 314, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922214630, "dur": 185, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922214816, "dur": 196, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922215015, "dur": 424, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922215441, "dur": 464, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922215906, "dur": 416, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922216324, "dur": 685, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922217011, "dur": 1471, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922218484, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922218557, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922218581, "dur": 406, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922218991, "dur": 1699, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922220691, "dur": 20, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922220713, "dur": 326, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922221042, "dur": 374, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922221418, "dur": 594, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922222014, "dur": 105, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922222121, "dur": 131, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922222254, "dur": 270, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922222525, "dur": 164, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922222691, "dur": 694, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922223387, "dur": 383, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922223772, "dur": 621, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922224394, "dur": 1043, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922225440, "dur": 446, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922225888, "dur": 480, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922226370, "dur": 118, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922226492, "dur": 525, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922227019, "dur": 570, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922227592, "dur": 457, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922228051, "dur": 1264, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922229317, "dur": 335, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922229653, "dur": 140, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922229796, "dur": 165, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922229962, "dur": 180, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922230144, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922230230, "dur": 143, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922230375, "dur": 245, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922230622, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922230728, "dur": 231, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922230963, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922230988, "dur": 185, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922231194, "dur": 9, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922231217, "dur": 436, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922231655, "dur": 980, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922232637, "dur": 153, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922232792, "dur": 181, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922232975, "dur": 168, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922233145, "dur": 168, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922233314, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922233393, "dur": 155, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922233551, "dur": 160, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922233713, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922233818, "dur": 2235, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922236058, "dur": 406, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922236466, "dur": 706, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922237175, "dur": 774, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922237951, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922238052, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922238116, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922238193, "dur": 160, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922238355, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922238428, "dur": 362, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922238792, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922238819, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922238859, "dur": 201, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922239062, "dur": 1143, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922240207, "dur": 552, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922240761, "dur": 111, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922240875, "dur": 249, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922241125, "dur": 214, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922241342, "dur": 101, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922241445, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922241527, "dur": 146, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922241675, "dur": 253, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922241930, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922241983, "dur": 543, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922242527, "dur": 421, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922242951, "dur": 124, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922243077, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922243159, "dur": 23, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922243184, "dur": 102, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922243289, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922243361, "dur": 116, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922243479, "dur": 211, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922243696, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922243700, "dur": 178, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922243880, "dur": 224, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922244106, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922244125, "dur": 233, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922244359, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922244381, "dur": 112, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922244496, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922244500, "dur": 130, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922244632, "dur": 158, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922244792, "dur": 174, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922244969, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922244971, "dur": 65, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922245041, "dur": 244, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922245288, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922245315, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922245388, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922245407, "dur": 194, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922245602, "dur": 80, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922245685, "dur": 266, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922245953, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922246020, "dur": 206, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922246229, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922246288, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922246356, "dur": 380, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922246737, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922246739, "dur": 122548, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922369293, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922369296, "dur": 57, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922369356, "dur": 47, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922369410, "dur": 33, "ph": "X", "name": "ReadAsync 7477", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922369448, "dur": 88, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922369538, "dur": 32, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922369572, "dur": 88, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922369661, "dur": 980, "ph": "X", "name": "ProcessMessages 2800", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922370643, "dur": 2179, "ph": "X", "name": "ReadAsync 2800", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922372829, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922372832, "dur": 190, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922373026, "dur": 173, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922373202, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922373204, "dur": 392, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922373599, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922373601, "dur": 576, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922374181, "dur": 547, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922374731, "dur": 387, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922375121, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922375124, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922375201, "dur": 618, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922375822, "dur": 745, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922376570, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922376573, "dur": 628, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922377205, "dur": 592, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922377800, "dur": 182, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922377985, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922377988, "dur": 637, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922378627, "dur": 237, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922378867, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922378869, "dur": 245, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922379120, "dur": 261, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922379385, "dur": 781, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922380170, "dur": 305, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922380476, "dur": 795, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922381273, "dur": 523, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922381799, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922381802, "dur": 242, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922382046, "dur": 262, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922382312, "dur": 276, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922382591, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922382593, "dur": 307, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922382904, "dur": 320, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922383226, "dur": 1357, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922384587, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922384589, "dur": 486, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922385077, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922385146, "dur": 211, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922385359, "dur": 511, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922385874, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922385877, "dur": 1246, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922387125, "dur": 355, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922387482, "dur": 2147, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922389631, "dur": 199, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922389833, "dur": 176, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922390010, "dur": 1252, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922391268, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922391271, "dur": 880, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922392154, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922392230, "dur": 654, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922392887, "dur": 1035, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922393929, "dur": 500, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922394436, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922394440, "dur": 97, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922394541, "dur": 109, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922394652, "dur": 64, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922394718, "dur": 161, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922394881, "dur": 164, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922395049, "dur": 272, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922395323, "dur": 273, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922395605, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922395608, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922395652, "dur": 105, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922395759, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922395793, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922395839, "dur": 78, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922395919, "dur": 99, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922396020, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922396044, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922396115, "dur": 80, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922396196, "dur": 187, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922396385, "dur": 111, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922396498, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922396557, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922396593, "dur": 127, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922396722, "dur": 139, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922396863, "dur": 58, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922396923, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922396945, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922397004, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922397041, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922397073, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922397150, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922397174, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922397245, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922397311, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922397336, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922397369, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922397427, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922397505, "dur": 118, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922397625, "dur": 93, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922397720, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922397746, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922397810, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922397866, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922397948, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922397973, "dur": 29, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922398004, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922398026, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922398049, "dur": 17, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922398068, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922398086, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922398115, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922398227, "dur": 135, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922398364, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922398452, "dur": 124, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922398582, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922398585, "dur": 51, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922398640, "dur": 151, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922398795, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922398797, "dur": 125, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922398925, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922398941, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922399034, "dur": 109, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960922399147, "dur": 1448101, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960923847270, "dur": 141, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960923847416, "dur": 4898, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960923852323, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960923852328, "dur": 9646, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960923861983, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960923861993, "dur": 51836, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960923913848, "dur": 8, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960923913860, "dur": 333279, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960924247148, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960924247151, "dur": 74, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960924247232, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960924247235, "dur": 43, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960924247281, "dur": 34, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960924247318, "dur": 86, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960924247406, "dur": 15, "ph": "X", "name": "ProcessMessages 7965", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960924247422, "dur": 3083, "ph": "X", "name": "ReadAsync 7965", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960924250508, "dur": 28225, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960924278739, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960924278741, "dur": 37, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960924278781, "dur": 50, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960924278834, "dur": 62, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960924278899, "dur": 59, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960924278959, "dur": 24, "ph": "X", "name": "ProcessMessages 5339", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960924278984, "dur": 2805, "ph": "X", "name": "ReadAsync 5339", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960924281791, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960924281792, "dur": 509, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960924282303, "dur": 5, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960924282309, "dur": 87, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960924282397, "dur": 8, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960924282406, "dur": 186, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960924282593, "dur": 234, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748960924282829, "dur": 2600, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 23505, "tid": 13, "ts": 1748960924296482, "dur": 1237, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 23505, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 23505, "tid": 8589934592, "ts": 1748960922024536, "dur": 106039, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 23505, "tid": 8589934592, "ts": 1748960922130577, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 23505, "tid": 8589934592, "ts": 1748960922130581, "dur": 3371, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 23505, "tid": 13, "ts": 1748960924297721, "dur": 8, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 23505, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 23505, "tid": 4294967296, "ts": 1748960921964421, "dur": 2322008, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 23505, "tid": 4294967296, "ts": 1748960921975687, "dur": 41451, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 23505, "tid": 4294967296, "ts": 1748960924286584, "dur": 4140, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 23505, "tid": 4294967296, "ts": 1748960924288356, "dur": 1568, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 23505, "tid": 4294967296, "ts": 1748960924290778, "dur": 9, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 23505, "tid": 13, "ts": 1748960924297731, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748960922034771, "dur": 15784, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748960922050564, "dur": 33184, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748960922083820, "dur": 146, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748960922083967, "dur": 193, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748960922084515, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_BB7D6B77AADC3636.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748960922084711, "dur": 405, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_1011BD4CAF44A078.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748960922085466, "dur": 334, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_18DC2EF7A0439C61.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748960922086697, "dur": 315, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_B5FD6E098A39A5D2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748960922087649, "dur": 220, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_42EA6A51FFD2E09A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748960922087921, "dur": 119, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F4C05CFCBFAEF91C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748960922089831, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748960922090370, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748960922090633, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.ref.dll_62FEAEE8B284189E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748960922094262, "dur": 306, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748960922095486, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748960922096547, "dur": 101, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748960922096822, "dur": 113, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748960922098387, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748960922099155, "dur": 368, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Shared.Editor.ref.dll_CBD4E358CB8CDF43.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748960922113987, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748960922125044, "dur": 108, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748960922125170, "dur": 126, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748960922134014, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748960922136874, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748960922146738, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748960922084169, "dur": 78732, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748960922162910, "dur": 2119707, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748960924282697, "dur": 55, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748960924282775, "dur": 748, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748960922084127, "dur": 78903, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922163035, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922163478, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922163574, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C9A2ED22AC71DAA3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922164014, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922164181, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FED4EADA5A496243.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922164628, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FED4EADA5A496243.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922164752, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_AAC09D6387BA9514.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922164937, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922165033, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A5396449E1C706BB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922165385, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922165516, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_D51B60744B48FB82.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922165652, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922165757, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_18DC2EF7A0439C61.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922166057, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922166254, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_A6062A75CA920C08.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922166553, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922166712, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_282E744BB5E921C6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922166861, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922166954, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_54CEF74303048885.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922167034, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922167117, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_4D1EEC228562B063.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922167347, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922167533, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_0DC95BA72EA6C3C0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922167701, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922167811, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FB9A8EFABECD3061.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922168025, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922168166, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_0240B089C917E98F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922168295, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922168458, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922168711, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7ADE8004EB70550A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922168789, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7ADE8004EB70550A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922168875, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748960922169086, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748960922169302, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748960922169499, "dur": 670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748960922170333, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922170402, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922170483, "dur": 1160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748960922171715, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922171826, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922171926, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922171996, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748960922172131, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922172186, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922172290, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922172385, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922172508, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922172566, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922172759, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922172902, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922173045, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922173108, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922173175, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922173248, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922173330, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922173401, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922173484, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748960922173908, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922173965, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922174038, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922174105, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922174242, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922175411, "dur": 1653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922177065, "dur": 2749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922179815, "dur": 1103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922180918, "dur": 988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922181906, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922182773, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922183414, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922184086, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922184733, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922185694, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922186548, "dur": 2069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922188617, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922189698, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922190570, "dur": 1035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922191606, "dur": 1276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922192882, "dur": 1269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922194151, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922195018, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922195712, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922196460, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922197112, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922197979, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922198901, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922199689, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922200576, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922201449, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922202195, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922202503, "dur": 20072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748960922222575, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922223036, "dur": 591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922223678, "dur": 14404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748960922238083, "dur": 364, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922238526, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922239127, "dur": 1729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748960922240857, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922241087, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll_059BC5464FF14C2B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922241138, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922241208, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922241634, "dur": 1450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748960922243084, "dur": 428, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922243559, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922243710, "dur": 1546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748960922245256, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922245627, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922245753, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748960922246166, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922246292, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748960922246352, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748960922246561, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922246694, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748960923842832, "dur": 359, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960922247972, "dur": 1595332, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748960923847469, "dur": 291, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748960923846589, "dur": 1321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748960923847953, "dur": 10805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748960923898383, "dur": 7487, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960924247253, "dur": 373, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960923908161, "dur": 339473, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748960924250475, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748960924250654, "dur": 31956, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922084128, "dur": 78881, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922163014, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748960922163210, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922163413, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DB357A1C5B5CB80B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922163800, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_F9A822B4784E7167.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922163994, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922164164, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_F3E4D7EAC2485D07.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922164628, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_F3E4D7EAC2485D07.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922164721, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_0726B0E54E5C8C45.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922164947, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_317A56715ABF89E0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922165189, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922165425, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2D2E9A4895907F63.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922165610, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922165808, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_CC9B8F296E9C6231.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922166035, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922166101, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0BF81E52D42208B4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922166270, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922166501, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_2A46121DF039C105.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922166675, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922166819, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_23B09F05B98149F7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922166954, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922167023, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_50D38B6F9A6D24D8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922167134, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922167306, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7B6D90F67FD0A1D6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922167438, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922167567, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7FABF25E82FCDDCD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922167728, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922167795, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_57DA0DA15F72E273.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922167957, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922168095, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_66BE93EAC03D0429.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922168258, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922168379, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_90AD9150DA4AFB38.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922168695, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748960922168996, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748960922169289, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748960922169343, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748960922169596, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922169688, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_336F3065DC28AE59.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922169765, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922169831, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922169888, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748960922170371, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922170522, "dur": 678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748960922171217, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748960922171735, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922171832, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922171900, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922171991, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922172071, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922172190, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922172270, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922172379, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922172546, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922172691, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922172749, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922172812, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922172907, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922173034, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922173178, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922173256, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922173346, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922173434, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922173572, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922173822, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922173917, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922174054, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922174194, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1748960922174257, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922174372, "dur": 1127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922175499, "dur": 1865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922177364, "dur": 2552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922179916, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922181010, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922182000, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922182834, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922183469, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922184135, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922184787, "dur": 958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922185745, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922186616, "dur": 2004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922188620, "dur": 1080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922189701, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922190568, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922191612, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922192905, "dur": 1269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922194174, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922195074, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922195751, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922196500, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922197157, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922198035, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922198932, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922199724, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922200611, "dur": 949, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922201560, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922202280, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922202500, "dur": 5594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748960922208095, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922208301, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.ref.dll_62FEAEE8B284189E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922208425, "dur": 1949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922210374, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922210432, "dur": 3792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748960922214237, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922214466, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ref.dll_F0EDAB69F288A2F3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922214558, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922214632, "dur": 1517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922216149, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922216212, "dur": 1050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922217263, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922217316, "dur": 3936, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748960922221252, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922221355, "dur": 1426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922222781, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922222842, "dur": 3480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748960922226322, "dur": 470, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922226818, "dur": 5972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748960922232790, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922232961, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_6DAF32D8644B89D6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922233042, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922233116, "dur": 5552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748960922238668, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922238750, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922239405, "dur": 2019, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748960922241425, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922241671, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922242040, "dur": 109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922242177, "dur": 64, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922242241, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922242584, "dur": 686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922243272, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748960922243407, "dur": 997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748960922244405, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922244468, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748960922244897, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748960922245128, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922245295, "dur": 125738, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922371035, "dur": 2775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748960922373858, "dur": 2907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748960922376765, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922376890, "dur": 2714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748960922379648, "dur": 3104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748960922382769, "dur": 3308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748960922386078, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922386152, "dur": 3698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748960922389896, "dur": 2038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748960922391945, "dur": 6731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748960922398677, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922398873, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922399111, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748960922399369, "dur": 1883244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922084123, "dur": 78814, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922162969, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748960922163164, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922163278, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_9CEC3B310263C3AF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748960922163896, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_BB7D6B77AADC3636.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748960922164136, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922164277, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_94E7D34F0A24E8EE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748960922164689, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_1B4EA2FCC997DC7B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748960922164823, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_EE2B3985795E2BC5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748960922164998, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922165091, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_173ADBFCCFB403B8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748960922165475, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7FC5D5B6D1C0F474.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748960922165664, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922165826, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_D09E55B94B6FBE69.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748960922166058, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922166236, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A894F34BC96CE2D6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748960922166522, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922166672, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_13682AC6E2C4EE7B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748960922166952, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922167007, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_AB798D326E6716CC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748960922167137, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922167312, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6ECCF42616E9E2B7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748960922167442, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922167543, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_796E436A12A0D05D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748960922167708, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922167834, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_1C9B2F49E4A7FD04.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748960922167991, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922168137, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_DB39C465F989DB9D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748960922168315, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748960922168803, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748960922168890, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748960922169271, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748960922169464, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748960922169648, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922169729, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_1C1CB7F65D73ACD6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748960922169857, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748960922170365, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922170456, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922170514, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922170572, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922170627, "dur": 605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748960922171261, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748960922171734, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922171858, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922171947, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922172009, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922172082, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922172157, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922172227, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922172356, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922172460, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748960922172938, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922173100, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922173203, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922173269, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922173339, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922173416, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922173550, "dur": 722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748960922174272, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922174374, "dur": 1143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922175517, "dur": 1858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922177375, "dur": 2544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922179920, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922181027, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922182026, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922182854, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922183490, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922184158, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922184799, "dur": 972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922185771, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922186671, "dur": 2010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922188682, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922189736, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922190604, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922191638, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922192918, "dur": 1237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922194156, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922195054, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922195723, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922196479, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922197142, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922198023, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922198915, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922199708, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922200610, "dur": 944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922201554, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922202271, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748960922202774, "dur": 4440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748960922207214, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922207367, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_D292A1F7EB1601C3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748960922207430, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922207496, "dur": 1655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748960922209151, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922209214, "dur": 9881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748960922219096, "dur": 2603, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922221706, "dur": 2317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748960922224072, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748960922224683, "dur": 16819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748960922241502, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922241861, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748960922242075, "dur": 1537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748960922243674, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748960922243793, "dur": 804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748960922244598, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922244775, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922245292, "dur": 125732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922371025, "dur": 3338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748960922374364, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922374444, "dur": 2763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748960922377257, "dur": 2114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748960922379391, "dur": 2970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748960922382361, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922382422, "dur": 2980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748960922385403, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922385456, "dur": 2954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748960922388410, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960922388475, "dur": 2978, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748960922391468, "dur": 7710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748960922399276, "dur": 1882614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748960924281963, "dur": 546, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748960924282511, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922084129, "dur": 78917, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922163056, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BA0B4FF91CE5F8A1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922163437, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922163568, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CD898CC368A4D4E9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922163866, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922163992, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_119E2929175A73E5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922164437, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DBF9BD3B0D79F91E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922164686, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_48780D15ACA6139C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922164807, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_557EEB5E99C128BC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922164969, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922165145, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_24BD6C6FEF5EF086.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922165408, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922165495, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_B6303FFB36F7BFF8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922165676, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922165862, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CF90820960D75094.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922166096, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922166172, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_D296BA1279712408.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922166446, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922166633, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9191DFAD59FAEA6A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922166854, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922166936, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_71635C60905BE81B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922167038, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922167123, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_10C02A94B188B9C3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922167329, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922167513, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D4B8FF3D259E96C1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922167688, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922167782, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_42DE6F6D94492967.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922167976, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922168114, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FA4CDBD8E90E39CA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922168264, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922168338, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_50F42E90481F425E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922168689, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748960922169071, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922169282, "dur": 1897, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748960922171179, "dur": 430, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922171661, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922171757, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748960922171888, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922171964, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922172037, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922172102, "dur": 946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748960922173049, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922173183, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922173256, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922173357, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922173427, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922173528, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922173774, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922173842, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922173905, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922173962, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922174025, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922174091, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922174269, "dur": 1164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922175434, "dur": 1687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922177146, "dur": 2697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922179844, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922180943, "dur": 1002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922181945, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922182798, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922183430, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922184101, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922184741, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922185680, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922186541, "dur": 2009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922188551, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922189712, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922190572, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922191632, "dur": 1279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922192911, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922194185, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922195077, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922195759, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922196505, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922197174, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922198039, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922198941, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922199782, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922200625, "dur": 970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922201595, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922202306, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922202506, "dur": 1039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748960922203546, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922203823, "dur": 1340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922205164, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922205219, "dur": 8334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748960922213554, "dur": 460, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922214019, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_EA61D4CF7C90DCE7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922214142, "dur": 766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922214956, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922215362, "dur": 1242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922216633, "dur": 2217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922218883, "dur": 3284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748960922222167, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922222318, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_1305DD42619CF31E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922222381, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922222441, "dur": 3488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748960922225929, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922226194, "dur": 4772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748960922230966, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922231261, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922231317, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922231949, "dur": 5437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748960922237386, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922237500, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922237696, "dur": 67, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922237763, "dur": 56, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922237819, "dur": 128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922237947, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922238573, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922239155, "dur": 3024, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748960922242180, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922242333, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922242485, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748960922243011, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922243259, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922243449, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748960922244087, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922244397, "dur": 831, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922245229, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748960922245298, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748960922245649, "dur": 125380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922371036, "dur": 3856, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748960922374893, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922374976, "dur": 3070, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748960922378073, "dur": 2397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonance.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748960922380484, "dur": 2881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748960922383408, "dur": 2083, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748960922385540, "dur": 3665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748960922389206, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922389263, "dur": 2775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748960922392039, "dur": 491, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922392534, "dur": 6459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748960922398994, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922399067, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960922399267, "dur": 1851195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960924250490, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748960924250650, "dur": 31953, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922084141, "dur": 78939, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922163088, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A97ABBD6C696F706.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922163556, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922163637, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5637F04715021BE0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922163961, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922164070, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_542089FB00C06F3A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922164511, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922164664, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922164770, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD7D90864D1DB0B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922165001, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922165085, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FD466DE9C9D83358.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922165489, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922165633, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_5D1BAF9C8C0FFD1F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922165794, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922165991, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_2793B80490192449.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922166114, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922166310, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_EE8FDF61FD2953D9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922166572, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_110AF0D569B2AA65.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922166857, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922166951, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_548678B7412B99C9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922167077, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922167172, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_D95259C03EE6A7C4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922167321, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922167471, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8D74880C622CE1AD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922167656, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922167738, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_43500FCF65A89BF3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922167892, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922167996, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_90196DBEEEA0E95D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922168137, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922168214, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1748960922168710, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922168799, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1748960922168882, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748960922168974, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1748960922169172, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748960922169272, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748960922169370, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1748960922169619, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_C7D65B20A28C6399.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922169736, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922169841, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748960922170304, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748960922170615, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922170750, "dur": 939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748960922171795, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922171871, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922171980, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922172094, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922172164, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922172257, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922172370, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922172483, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748960922173105, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922173207, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748960922173811, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922173923, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922174045, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922174149, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748960922174216, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922174317, "dur": 1137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922175454, "dur": 1775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922177230, "dur": 2624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922179854, "dur": 1096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922180950, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922181815, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922182707, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922183352, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922184014, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922184669, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922185569, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922186432, "dur": 1924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922188356, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922189598, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922190421, "dur": 980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922191420, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922191477, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748960922191528, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922191646, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922192926, "dur": 1252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922194178, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922195083, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922195753, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922196495, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922197150, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922198025, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922198923, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922199722, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922200601, "dur": 923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922201549, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922202266, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922202604, "dur": 2576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748960922205180, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922205518, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Utilities.ref.dll_9587680413563429.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922205638, "dur": 1942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922207601, "dur": 5774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748960922213376, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922213784, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Editor.ref.dll_EA1A6D1CB62C8BFD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922213887, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922213952, "dur": 1300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922215285, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922215661, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922215714, "dur": 1561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922217307, "dur": 1977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922219313, "dur": 2948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748960922222262, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922222436, "dur": 3149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748960922225585, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922225737, "dur": 890, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922226671, "dur": 2650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748960922229321, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922229543, "dur": 1647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922231238, "dur": 6956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748960922238194, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922238276, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PerformanceTesting.ref.dll_A9B74A479D61B912.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922238378, "dur": 673, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922239069, "dur": 2257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748960922241427, "dur": 93, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922241570, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922241835, "dur": 91, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922242196, "dur": 66, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922242263, "dur": 97, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922242586, "dur": 976, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922243564, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922243865, "dur": 724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748960922244590, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922244763, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922245268, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748960922245339, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748960922245716, "dur": 125345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922371062, "dur": 4384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748960922375488, "dur": 3322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748960922378848, "dur": 2594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748960922381485, "dur": 3390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748960922384923, "dur": 2313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748960922387277, "dur": 2865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748960922390142, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922390223, "dur": 3853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748960922394078, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922394311, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922394704, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922394771, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922394864, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922395044, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922395104, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922395171, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922395305, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922395365, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922395633, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922395754, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922396511, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922396704, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922398552, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748960922398659, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922398937, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922399239, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748960922399480, "dur": 1883127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922084118, "dur": 78799, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922163512, "dur": 586, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 6, "ts": 1748960922164098, "dur": 2627, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 6, "ts": 1748960922166725, "dur": 733, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 6, "ts": 1748960922162922, "dur": 4536, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922167458, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_458C519AD2A9288F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748960922167619, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922167712, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_3104EC13C41E6B6B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748960922167855, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922168009, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5836CD6851EC9E19.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748960922168174, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922168273, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748960922168787, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748960922169050, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922169108, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748960922169388, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748960922169650, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922169744, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_E81F8781B3EEDB59.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748960922169830, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922169883, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748960922170336, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922170431, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748960922170938, "dur": 894, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748960922171832, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922171895, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922171973, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922172056, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922172116, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922172177, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922172307, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922172390, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922172549, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748960922172951, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922173078, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922173165, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922173245, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922173334, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922173469, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922173577, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748960922173909, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922173970, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922174037, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922174101, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922174254, "dur": 1423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922175678, "dur": 2146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922177828, "dur": 2316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922180144, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922181170, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922182162, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922182935, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922183572, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922184238, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922184879, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922185839, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922187018, "dur": 1892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922188911, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922189833, "dur": 914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922190747, "dur": 1088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922191835, "dur": 1253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922193090, "dur": 1239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922194329, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922195214, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922195871, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922196611, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922197318, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922198191, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922199058, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922199876, "dur": 819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922200695, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922201551, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922202264, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748960922202517, "dur": 645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748960922203163, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922203497, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Searcher.Editor.ref.dll_188082AF4C893B1A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748960922203556, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922203624, "dur": 1050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748960922204720, "dur": 5688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748960922210408, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922210744, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_70C63ADD63245F54.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748960922210840, "dur": 2415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748960922213256, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922213330, "dur": 14268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748960922227598, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922227873, "dur": 4899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748960922232773, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922232990, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Updater.ref.dll_C51C3847CE9500F9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748960922233086, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922233217, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922233355, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922233482, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922233592, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922233708, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748960922233764, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922233916, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922234000, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922234106, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922234169, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922234371, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922234558, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922235540, "dur": 1262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922236802, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922237576, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922237764, "dur": 72, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922237878, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922238185, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922238841, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748960922239385, "dur": 1938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748960922241323, "dur": 434, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922241761, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_C5B7873C632B963A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748960922241843, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922242032, "dur": 119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922242180, "dur": 81, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922242452, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922242592, "dur": 2697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922245290, "dur": 1243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922246534, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748960922246630, "dur": 124411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922371042, "dur": 2172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748960922373216, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922373288, "dur": 2484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748960922375774, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922375844, "dur": 3199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748960922379102, "dur": 2820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnity.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748960922381958, "dur": 3289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748960922385286, "dur": 2400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748960922387686, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922387745, "dur": 2395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748960922390140, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922390255, "dur": 2638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748960922392894, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748960922392962, "dur": 6500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748960922399478, "dur": 1883133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922084158, "dur": 78957, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922163122, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748960922163770, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_86EBD7C4DEEB8CFD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748960922163989, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922164103, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_3BDCA2C56F5A3F82.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748960922164445, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_1011BD4CAF44A078.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748960922164675, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_0F150CB94F2F22E2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748960922164918, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_0F150CB94F2F22E2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748960922164972, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_3A20567EF69EC9A7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748960922165139, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922165385, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F4C05CFCBFAEF91C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748960922165666, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922165853, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_EEFB9A6B4613348E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748960922166073, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922166224, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_AA0CAB1786F0F828.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748960922166515, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922166613, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_1CA607ABAD2FE49C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748960922166897, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_B2F5068140D067F0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748960922166987, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922167132, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_9927031187632C7E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748960922167327, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922167449, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_242B47D8D2C44484.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748960922167635, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922167721, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_4E33C3C55AB9064E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748960922167838, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922167925, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_DAACB4F719294F92.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748960922168089, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922168204, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_42EA6A51FFD2E09A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748960922168287, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922168357, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_42EA6A51FFD2E09A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748960922168446, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_E38DB30ED3E699A5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748960922168727, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748960922168846, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748960922168935, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748960922168986, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922169049, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748960922169299, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748960922169487, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748960922169714, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922169787, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748960922170242, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748960922170413, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922170496, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748960922170682, "dur": 942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748960922171651, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748960922171734, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922171848, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922171959, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922172017, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922172090, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922172152, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922172219, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922172352, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922172421, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922172519, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922172609, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748960922172883, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922172948, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922173063, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922173146, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922173227, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922173299, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922173376, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922173449, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922173562, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922173618, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748960922173913, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922174043, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922174132, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922174279, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922175424, "dur": 1646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922177071, "dur": 2776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922179847, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922180937, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922181934, "dur": 862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922182796, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922183434, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922184108, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922184752, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922185713, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922186604, "dur": 2045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922188649, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922189704, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922190575, "dur": 1015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922191591, "dur": 1272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922192864, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922194131, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922195015, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922195697, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922196445, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922197095, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922197938, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922198850, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922199625, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922200504, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922200941, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922201727, "dur": 752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748960922202480, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922202546, "dur": 8159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748960922210705, "dur": 456, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922211242, "dur": 1896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748960922213139, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922213250, "dur": 6759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748960922220009, "dur": 954, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922220976, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922221049, "dur": 1469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748960922222519, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922222576, "dur": 4412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748960922226988, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922227319, "dur": 4544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1748960922231932, "dur": 4173, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922369523, "dur": 439, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922236411, "dur": 133563, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1748960922371024, "dur": 2363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748960922373387, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922373449, "dur": 2713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748960922376163, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922376243, "dur": 4028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748960922380314, "dur": 1922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748960922382266, "dur": 2444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748960922384753, "dur": 2935, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonanceEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748960922387688, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922387762, "dur": 2191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748960922389983, "dur": 3227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748960922393210, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748960922393361, "dur": 5973, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748960922399360, "dur": 1883240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922084165, "dur": 78978, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922163145, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922163548, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_33EF23AB874F1C0F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922163877, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922163952, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_620BC00CDC1CB284.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922164172, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922164388, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_3DB4BB1D7CF8DED1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922164653, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_7C523B7BB1DAFB72.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922164814, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922164973, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_73BBD5453873209A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922165383, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922165502, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_1D189CF611149EDC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922165691, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922165871, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F44CFBD4B87405AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922166114, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922166282, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_C90DAD4D0E34B513.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922166547, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922166719, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_FF6DF2438A8881EC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922166920, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922166981, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_3EDBDDC0989E0EE4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922167126, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922167264, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_B5FD6E098A39A5D2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922167462, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922167607, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6F2F737294BA2646.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922167758, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922167856, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_2AF15A5FC73B3288.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922168033, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922168195, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C20199F83674ABBE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922168303, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748960922168844, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748960922168925, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922169156, "dur": 2165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748960922171349, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922171403, "dur": 19495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748960922190899, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922191039, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_DE1D13444BAAFBE7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922191167, "dur": 3707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922194932, "dur": 6339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748960922201272, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922201532, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_BD0E97F52A0DF729.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922201658, "dur": 982, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922202665, "dur": 2353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748960922205019, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922205374, "dur": 2221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922207595, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922207704, "dur": 5240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748960922212944, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922213166, "dur": 1886, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922215053, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922215110, "dur": 3459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748960922218569, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922218858, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922218922, "dur": 3180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748960922222102, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922222345, "dur": 5592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748960922227938, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922228330, "dur": 1506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922229873, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_CBF917C274624EE2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922229944, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922230071, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_AAF6E30B590FA4FC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922230143, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922230245, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll_4AD9CE269414E4CC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922230437, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922230537, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rendering.LightTransport.Editor.ref.dll_B243E984A9C458BA.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922230622, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922230717, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Cursor.Editor.ref.dll_CD3F41468EC5D7E9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922230813, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922230927, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922231045, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_F37C0F1BA2EC04B9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922231154, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922231262, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922231487, "dur": 2344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922233858, "dur": 4763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748960922238621, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922238688, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922239191, "dur": 1267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748960922240458, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922240547, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922241014, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922241429, "dur": 100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922241575, "dur": 104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922241680, "dur": 97, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922241777, "dur": 56, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922241875, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922242129, "dur": 1364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748960922243494, "dur": 471, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922244011, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922244119, "dur": 818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748960922244937, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922245223, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922245294, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748960922245735, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922245945, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922246014, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748960922246223, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922246532, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748960922246621, "dur": 124431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922371053, "dur": 1952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748960922373061, "dur": 2236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748960922375297, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922375365, "dur": 2852, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748960922378257, "dur": 2278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748960922380593, "dur": 2490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748960922383120, "dur": 2990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748960922386111, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922386185, "dur": 3460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748960922389680, "dur": 2796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748960922392477, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960922392607, "dur": 6562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748960922399250, "dur": 1447677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960923847858, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748960923846930, "dur": 5834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748960923859334, "dur": 2738, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960924278803, "dur": 347, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748960923863982, "dur": 415176, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748960924281870, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748960924281867, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748960924282002, "dur": 568, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748960924284773, "dur": 552, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 23505, "tid": 13, "ts": 1748960924298033, "dur": 2396, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 23505, "tid": 13, "ts": 1748960924300465, "dur": 1221, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 23505, "tid": 13, "ts": 1748960924294437, "dur": 7864, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}