{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 23505, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 23505, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 23505, "tid": 125, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 23505, "tid": 125, "ts": 1748961755101795, "dur": 395, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 23505, "tid": 125, "ts": 1748961755105745, "dur": 669, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 23505, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 23505, "tid": 1, "ts": 1748961752471796, "dur": 7222, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 23505, "tid": 1, "ts": 1748961752479021, "dur": 71093, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 23505, "tid": 1, "ts": 1748961752550121, "dur": 84220, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 23505, "tid": 125, "ts": 1748961755106428, "dur": 22, "ph": "X", "name": "", "args": {}}, {"pid": 23505, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752470066, "dur": 7637, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752477705, "dur": 2617530, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752478454, "dur": 2426, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752480883, "dur": 1421, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752482306, "dur": 17644, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752499959, "dur": 320, "ph": "X", "name": "ProcessMessages 7157", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752500322, "dur": 36, "ph": "X", "name": "ReadAsync 7157", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752500360, "dur": 4, "ph": "X", "name": "ProcessMessages 8177", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752500365, "dur": 43, "ph": "X", "name": "ReadAsync 8177", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752500409, "dur": 1, "ph": "X", "name": "ProcessMessages 1382", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752500410, "dur": 45, "ph": "X", "name": "ReadAsync 1382", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752500458, "dur": 1, "ph": "X", "name": "ProcessMessages 1301", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752500460, "dur": 22, "ph": "X", "name": "ReadAsync 1301", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752500483, "dur": 1, "ph": "X", "name": "ProcessMessages 1073", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752500484, "dur": 47, "ph": "X", "name": "ReadAsync 1073", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752500541, "dur": 120, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752500663, "dur": 1, "ph": "X", "name": "ProcessMessages 1745", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752500670, "dur": 18, "ph": "X", "name": "ReadAsync 1745", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752500690, "dur": 1, "ph": "X", "name": "ProcessMessages 3128", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752500692, "dur": 21, "ph": "X", "name": "ReadAsync 3128", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752500716, "dur": 642, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752501359, "dur": 3, "ph": "X", "name": "ProcessMessages 8149", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752501971, "dur": 22, "ph": "X", "name": "ReadAsync 8149", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752501995, "dur": 3, "ph": "X", "name": "ProcessMessages 7064", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752501998, "dur": 355, "ph": "X", "name": "ReadAsync 7064", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752502355, "dur": 2, "ph": "X", "name": "ProcessMessages 2578", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752502358, "dur": 51, "ph": "X", "name": "ReadAsync 2578", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752502411, "dur": 46, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752502459, "dur": 398, "ph": "X", "name": "ReadAsync 979", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752502859, "dur": 2, "ph": "X", "name": "ProcessMessages 4540", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752502862, "dur": 89, "ph": "X", "name": "ReadAsync 4540", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752502965, "dur": 29, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752502995, "dur": 1, "ph": "X", "name": "ProcessMessages 923", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752502997, "dur": 32, "ph": "X", "name": "ReadAsync 923", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752503033, "dur": 37, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752503073, "dur": 32, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752503109, "dur": 40, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752503152, "dur": 1, "ph": "X", "name": "ProcessMessages 1687", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752503154, "dur": 28, "ph": "X", "name": "ReadAsync 1687", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752503184, "dur": 1, "ph": "X", "name": "ProcessMessages 899", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752503185, "dur": 24, "ph": "X", "name": "ReadAsync 899", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752503211, "dur": 15, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752503229, "dur": 374, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752503604, "dur": 1, "ph": "X", "name": "ProcessMessages 1900", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752503607, "dur": 310, "ph": "X", "name": "ReadAsync 1900", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752503918, "dur": 2, "ph": "X", "name": "ProcessMessages 4080", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752503921, "dur": 202, "ph": "X", "name": "ReadAsync 4080", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752504127, "dur": 21, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752504151, "dur": 1, "ph": "X", "name": "ProcessMessages 1450", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752504153, "dur": 37, "ph": "X", "name": "ReadAsync 1450", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752504191, "dur": 1, "ph": "X", "name": "ProcessMessages 1088", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752504195, "dur": 20, "ph": "X", "name": "ReadAsync 1088", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752504217, "dur": 48, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752504271, "dur": 1, "ph": "X", "name": "ProcessMessages 1235", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752504272, "dur": 18, "ph": "X", "name": "ReadAsync 1235", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752504293, "dur": 43, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752504338, "dur": 347, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752504688, "dur": 20, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752504709, "dur": 2, "ph": "X", "name": "ProcessMessages 2918", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752504713, "dur": 64, "ph": "X", "name": "ReadAsync 2918", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752504779, "dur": 200, "ph": "X", "name": "ReadAsync 950", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752504982, "dur": 1, "ph": "X", "name": "ProcessMessages 1097", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752504983, "dur": 18, "ph": "X", "name": "ReadAsync 1097", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752505003, "dur": 1, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752505004, "dur": 405, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752505412, "dur": 2, "ph": "X", "name": "ProcessMessages 4120", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752505415, "dur": 5212, "ph": "X", "name": "ReadAsync 4120", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752510628, "dur": 4, "ph": "X", "name": "ProcessMessages 8191", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752510633, "dur": 25, "ph": "X", "name": "ReadAsync 8191", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752510661, "dur": 21301, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752531981, "dur": 5, "ph": "X", "name": "ProcessMessages 8123", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752531995, "dur": 47, "ph": "X", "name": "ReadAsync 8123", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752532046, "dur": 5, "ph": "X", "name": "ProcessMessages 8088", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752532061, "dur": 4838, "ph": "X", "name": "ReadAsync 8088", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752536901, "dur": 1, "ph": "X", "name": "ProcessMessages 1303", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752536903, "dur": 1386, "ph": "X", "name": "ReadAsync 1303", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752538290, "dur": 4, "ph": "X", "name": "ProcessMessages 8124", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752538297, "dur": 143, "ph": "X", "name": "ReadAsync 8124", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752538443, "dur": 1, "ph": "X", "name": "ProcessMessages 3408", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752538445, "dur": 28, "ph": "X", "name": "ReadAsync 3408", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752538476, "dur": 34, "ph": "X", "name": "ReadAsync 1322", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752538512, "dur": 197, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752541163, "dur": 38, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752541206, "dur": 3, "ph": "X", "name": "ProcessMessages 8179", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752541372, "dur": 27, "ph": "X", "name": "ReadAsync 8179", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752541400, "dur": 1, "ph": "X", "name": "ProcessMessages 1694", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752541402, "dur": 24, "ph": "X", "name": "ReadAsync 1694", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752541431, "dur": 1252, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752542685, "dur": 1, "ph": "X", "name": "ProcessMessages 1155", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752543276, "dur": 27, "ph": "X", "name": "ReadAsync 1155", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752543304, "dur": 3, "ph": "X", "name": "ProcessMessages 8145", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752543309, "dur": 216, "ph": "X", "name": "ReadAsync 8145", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752543527, "dur": 81, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752543609, "dur": 2, "ph": "X", "name": "ProcessMessages 5041", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752543615, "dur": 24, "ph": "X", "name": "ReadAsync 5041", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752543644, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752543645, "dur": 30, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752543678, "dur": 36, "ph": "X", "name": "ReadAsync 948", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752543715, "dur": 1, "ph": "X", "name": "ProcessMessages 791", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752543716, "dur": 38, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752543757, "dur": 23, "ph": "X", "name": "ReadAsync 1254", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752543783, "dur": 44, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752543829, "dur": 57, "ph": "X", "name": "ReadAsync 1270", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752543887, "dur": 1, "ph": "X", "name": "ProcessMessages 1083", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752543889, "dur": 196, "ph": "X", "name": "ReadAsync 1083", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752544087, "dur": 115, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752544204, "dur": 37, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752544243, "dur": 35, "ph": "X", "name": "ReadAsync 916", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752544281, "dur": 785, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752545377, "dur": 4, "ph": "X", "name": "ProcessMessages 6728", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752545382, "dur": 206, "ph": "X", "name": "ReadAsync 6728", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752545633, "dur": 101, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752545736, "dur": 459, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752546198, "dur": 201, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752546401, "dur": 584, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752546987, "dur": 44, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752547032, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752547034, "dur": 141, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752547177, "dur": 882, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752548061, "dur": 46, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752548109, "dur": 220, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752548383, "dur": 5, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752548393, "dur": 62, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752548457, "dur": 555, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752549014, "dur": 21, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752549037, "dur": 697, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752549737, "dur": 146, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752549885, "dur": 532, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752550419, "dur": 179, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752550600, "dur": 701, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752551304, "dur": 148, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752551454, "dur": 463, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752551920, "dur": 219, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752552141, "dur": 887, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752553031, "dur": 588, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752553621, "dur": 124, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752553748, "dur": 787, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752554538, "dur": 128, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752554668, "dur": 592, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752555263, "dur": 224, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752555490, "dur": 496, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752555988, "dur": 595, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752556587, "dur": 312, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752556901, "dur": 400, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752557304, "dur": 37, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752557344, "dur": 147, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752557492, "dur": 1046, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752558551, "dur": 154, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752559088, "dur": 25, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752559114, "dur": 1, "ph": "X", "name": "ProcessMessages 1175", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752559116, "dur": 896, "ph": "X", "name": "ReadAsync 1175", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752560024, "dur": 691, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752560727, "dur": 25, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752560753, "dur": 1, "ph": "X", "name": "ProcessMessages 2219", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752560755, "dur": 292, "ph": "X", "name": "ReadAsync 2219", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752561049, "dur": 94, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752561145, "dur": 17, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752561164, "dur": 28, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752561194, "dur": 18, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752561214, "dur": 47, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752561264, "dur": 173, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752561439, "dur": 191, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752561632, "dur": 55, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752561689, "dur": 20, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752561711, "dur": 168, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752561881, "dur": 277, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752562159, "dur": 1, "ph": "X", "name": "ProcessMessages 2198", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752562161, "dur": 491, "ph": "X", "name": "ReadAsync 2198", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752562817, "dur": 1, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752562819, "dur": 649, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752563474, "dur": 88, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752563564, "dur": 642, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752565406, "dur": 40, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752565447, "dur": 1, "ph": "X", "name": "ProcessMessages 1648", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752565450, "dur": 641, "ph": "X", "name": "ReadAsync 1648", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752566093, "dur": 1, "ph": "X", "name": "ProcessMessages 1410", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752566095, "dur": 242, "ph": "X", "name": "ReadAsync 1410", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752566342, "dur": 77, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752566421, "dur": 432, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752566856, "dur": 425, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752567284, "dur": 4919, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752572205, "dur": 4, "ph": "X", "name": "ProcessMessages 7194", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752572210, "dur": 34, "ph": "X", "name": "ReadAsync 7194", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752572247, "dur": 296, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752572545, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752572546, "dur": 39, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752572588, "dur": 598, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752573188, "dur": 197, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752573387, "dur": 333, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752573723, "dur": 966, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752574690, "dur": 1, "ph": "X", "name": "ProcessMessages 1736", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752574693, "dur": 43, "ph": "X", "name": "ReadAsync 1736", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752574738, "dur": 591, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752575332, "dur": 1277, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752576772, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752576774, "dur": 32, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752576807, "dur": 1217, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752578026, "dur": 108, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752578136, "dur": 2, "ph": "X", "name": "ProcessMessages 3213", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752578139, "dur": 98, "ph": "X", "name": "ReadAsync 3213", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752578280, "dur": 165, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752578447, "dur": 17, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752578493, "dur": 17, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752578513, "dur": 646, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752579161, "dur": 328, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752579491, "dur": 315, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752579809, "dur": 93, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752579909, "dur": 28, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752579940, "dur": 29, "ph": "X", "name": "ReadAsync 830", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752579971, "dur": 192, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752580165, "dur": 248, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752580415, "dur": 1, "ph": "X", "name": "ProcessMessages 1904", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752580417, "dur": 147, "ph": "X", "name": "ReadAsync 1904", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752580565, "dur": 251, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752580817, "dur": 1, "ph": "X", "name": "ProcessMessages 1862", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752580819, "dur": 202, "ph": "X", "name": "ReadAsync 1862", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752581022, "dur": 1, "ph": "X", "name": "ProcessMessages 1309", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752581033, "dur": 26, "ph": "X", "name": "ReadAsync 1309", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752581061, "dur": 1, "ph": "X", "name": "ProcessMessages 879", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752581062, "dur": 340, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752581404, "dur": 686, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752582092, "dur": 341, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752582434, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752582457, "dur": 60, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752582519, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752582554, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752582642, "dur": 12, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752582662, "dur": 642, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752583305, "dur": 2, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752583307, "dur": 319, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752583642, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752583644, "dur": 20, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752583666, "dur": 155, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752583823, "dur": 292, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752584117, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752584118, "dur": 120, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752584241, "dur": 122, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752584365, "dur": 24, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752584391, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752584411, "dur": 310, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752584723, "dur": 43, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752584776, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752584829, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752584851, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752584895, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752584966, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752585036, "dur": 88, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752585127, "dur": 55, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752585183, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752585219, "dur": 5, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752585225, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752585246, "dur": 113, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752585361, "dur": 58, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752585421, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752585454, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752585501, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752585564, "dur": 297, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752585862, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752585867, "dur": 232, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752586102, "dur": 330, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752586434, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752586503, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752586543, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752586646, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752586651, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752586727, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752586729, "dur": 95, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752586825, "dur": 155, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752586983, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752587021, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752587091, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752587094, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752587138, "dur": 62, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752587202, "dur": 166, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752587369, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752587419, "dur": 189, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752587610, "dur": 62, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752587674, "dur": 44, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752587720, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752587820, "dur": 353, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752588177, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752588180, "dur": 88, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752588271, "dur": 91, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752588364, "dur": 150, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752588517, "dur": 201, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752588720, "dur": 174, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752588896, "dur": 55, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752588953, "dur": 209, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752589164, "dur": 61, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752589227, "dur": 171, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752589400, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752589422, "dur": 153, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752589576, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752589578, "dur": 144, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752589723, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752589728, "dur": 69, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752589801, "dur": 15, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752589819, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752589857, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752589940, "dur": 182, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752590124, "dur": 168, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752590295, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752590299, "dur": 82, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752590384, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752590403, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752590433, "dur": 122, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752590563, "dur": 112, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752590678, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752590706, "dur": 105, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752590813, "dur": 57, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752590873, "dur": 151, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752591026, "dur": 75, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752591102, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752591121, "dur": 80, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752591365, "dur": 69, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752591435, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752591437, "dur": 73, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752591512, "dur": 101, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752591614, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752591617, "dur": 161, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752591780, "dur": 62, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752591844, "dur": 64, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752591925, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752591951, "dur": 62, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752592015, "dur": 75, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752592092, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752592116, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752592199, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752592225, "dur": 76, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752592303, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752592381, "dur": 5729, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752598115, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752598117, "dur": 142, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752598261, "dur": 491, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752598755, "dur": 524, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752599281, "dur": 705, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752599989, "dur": 102, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752600093, "dur": 482, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752600577, "dur": 125, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752600705, "dur": 897, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752601604, "dur": 186, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752601792, "dur": 178, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752601972, "dur": 186, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752602161, "dur": 824, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752602987, "dur": 962, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752603951, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752603988, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752604072, "dur": 106, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752604180, "dur": 314, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752604496, "dur": 413, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752604911, "dur": 497, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752605414, "dur": 447, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752605863, "dur": 1429, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752607295, "dur": 14, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752607311, "dur": 1399, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752608712, "dur": 6, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752608719, "dur": 1100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752614716, "dur": 58, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752614776, "dur": 227, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752615004, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752615006, "dur": 1676, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752616684, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752616713, "dur": 184, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752616898, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752616903, "dur": 341, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752617246, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752617248, "dur": 174, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752617425, "dur": 568, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752617995, "dur": 1750, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752619754, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752619777, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752619817, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752619910, "dur": 409, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752620322, "dur": 812, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752621136, "dur": 197, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752621336, "dur": 856, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752622194, "dur": 371, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752622567, "dur": 957, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752623531, "dur": 117, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752623651, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752623654, "dur": 2293, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752625954, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752625957, "dur": 336, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752626297, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752626299, "dur": 725, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752627026, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752627112, "dur": 141, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752627256, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752627287, "dur": 187, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752627477, "dur": 88, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752627567, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752627626, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752627716, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752627799, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752627840, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752627930, "dur": 111, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752628043, "dur": 692, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752628737, "dur": 218, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752628957, "dur": 135, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752629094, "dur": 279, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752629376, "dur": 183, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752629561, "dur": 223, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752629787, "dur": 149, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752629938, "dur": 515, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752630455, "dur": 136, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752630594, "dur": 600, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752631195, "dur": 268, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752631465, "dur": 371, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752631839, "dur": 224, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752632069, "dur": 108, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752632179, "dur": 1251, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752633431, "dur": 133, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752633566, "dur": 249, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752633821, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752633826, "dur": 168, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752633997, "dur": 79, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752634079, "dur": 128, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752634209, "dur": 106, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752634317, "dur": 336, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752634655, "dur": 518, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752635176, "dur": 333, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752635512, "dur": 401, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752635914, "dur": 325, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752636241, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752636343, "dur": 444, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752636790, "dur": 523, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752637315, "dur": 169, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752637487, "dur": 189, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752637678, "dur": 13525, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752651208, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752651213, "dur": 669, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752651898, "dur": 313, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752652218, "dur": 1384, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752653605, "dur": 198, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752653813, "dur": 154, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752653968, "dur": 3545, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752657517, "dur": 162, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752657682, "dur": 1203, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752658886, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752658889, "dur": 2684, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752661581, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752661590, "dur": 710, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752662302, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752662303, "dur": 456, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752662763, "dur": 104, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752662870, "dur": 1431, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752664304, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752664305, "dur": 210, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752664521, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752664526, "dur": 75, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752664609, "dur": 363, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752664973, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752664977, "dur": 79768, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752744751, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752744754, "dur": 41, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752744800, "dur": 34, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752745982, "dur": 34, "ph": "X", "name": "ReadAsync 7477", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752746028, "dur": 39, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752746072, "dur": 30, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752746105, "dur": 20, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752746126, "dur": 1209, "ph": "X", "name": "ProcessMessages 2800", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752747337, "dur": 2255, "ph": "X", "name": "ReadAsync 2800", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752749655, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752749656, "dur": 222, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752749881, "dur": 106, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752749992, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752749996, "dur": 540, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752750539, "dur": 261, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752750806, "dur": 761, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752751569, "dur": 1080, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752752651, "dur": 226, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752752879, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752752993, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752752996, "dur": 166, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752753164, "dur": 514, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752753681, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752753684, "dur": 910, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752754596, "dur": 809, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752755407, "dur": 498, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752755907, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752756007, "dur": 142, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752756152, "dur": 952, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752757109, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752757112, "dur": 1077, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752758196, "dur": 269, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752758467, "dur": 944, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752759413, "dur": 391, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752759809, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752759811, "dur": 766, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752760580, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752760665, "dur": 380, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752761046, "dur": 467, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752761517, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752761519, "dur": 701, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752762223, "dur": 242, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752762466, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752762556, "dur": 388, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752762945, "dur": 353, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752763432, "dur": 1384, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752764821, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752764825, "dur": 419, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752765247, "dur": 212, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752765461, "dur": 210, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752765674, "dur": 316, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752765992, "dur": 737, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752766731, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752766735, "dur": 953, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752767690, "dur": 191, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752767883, "dur": 467, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752768352, "dur": 542, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752768896, "dur": 228, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752769126, "dur": 918, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752770049, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752770053, "dur": 480, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752770535, "dur": 137, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752770675, "dur": 86, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752770762, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752770821, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752770865, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752770948, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752770976, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752771003, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752771061, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752771093, "dur": 68, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752771163, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752771186, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752771245, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752771273, "dur": 108, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752771389, "dur": 25, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752771416, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752771447, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752771468, "dur": 32, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752771502, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752771526, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752771543, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752771575, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752771627, "dur": 74, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752771703, "dur": 17, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752771722, "dur": 63, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752771786, "dur": 34, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752771823, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752771844, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752771885, "dur": 126, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752772014, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752772032, "dur": 224, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752772257, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752772259, "dur": 64, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752772324, "dur": 83, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752772409, "dur": 155, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752772567, "dur": 114, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752772682, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752772686, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752772732, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752772792, "dur": 67, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752772861, "dur": 68, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752772932, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752772935, "dur": 231, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752773167, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752773171, "dur": 281, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752773455, "dur": 128, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961752773585, "dur": 1999329, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961754772946, "dur": 99, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961754773049, "dur": 10064, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961754783126, "dur": 6, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961754783136, "dur": 5744, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961754788887, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961754788892, "dur": 178855, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961754967751, "dur": 18, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961754967770, "dur": 2673, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961754970446, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961754970449, "dur": 60746, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961755031201, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961755031204, "dur": 47, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961755031255, "dur": 88, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961755031350, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961755031354, "dur": 77, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961755031434, "dur": 58, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961755031493, "dur": 32, "ph": "X", "name": "ProcessMessages 5339", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961755031526, "dur": 3366, "ph": "X", "name": "ReadAsync 5339", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961755034895, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961755034898, "dur": 203, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961755035102, "dur": 50, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961755035164, "dur": 50957, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961755086127, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961755086130, "dur": 64, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961755086205, "dur": 35, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961755086242, "dur": 51, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961755086297, "dur": 26, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961755086324, "dur": 19, "ph": "X", "name": "ProcessMessages 7965", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961755086344, "dur": 3167, "ph": "X", "name": "ReadAsync 7965", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961755089519, "dur": 6, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961755089550, "dur": 588, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961755090141, "dur": 36, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961755090178, "dur": 211, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961755090393, "dur": 351, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748961755090747, "dur": 4271, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 23505, "tid": 125, "ts": 1748961755106451, "dur": 2132, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 23505, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 23505, "tid": 8589934592, "ts": 1748961752467593, "dur": 166805, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 23505, "tid": 8589934592, "ts": 1748961752634400, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 23505, "tid": 8589934592, "ts": 1748961752634406, "dur": 1518, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 23505, "tid": 125, "ts": 1748961755108585, "dur": 13, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 23505, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 23505, "tid": 4294967296, "ts": 1748961752385293, "dur": 2711212, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 23505, "tid": 4294967296, "ts": 1748961752402730, "dur": 62091, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 23505, "tid": 4294967296, "ts": 1748961755096578, "dur": 2911, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 23505, "tid": 4294967296, "ts": 1748961755098562, "dur": 40, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 23505, "tid": 4294967296, "ts": 1748961755099539, "dur": 10, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 23505, "tid": 125, "ts": 1748961755108600, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748961752475077, "dur": 3292, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748961752478380, "dur": 21192, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748961752499623, "dur": 165, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748961752499788, "dur": 101, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748961752500500, "dur": 112, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_B6303FFB36F7BFF8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748961752501261, "dur": 359, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FA4CDBD8E90E39CA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748961752506555, "dur": 4336, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748961752511544, "dur": 18304, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748961752530286, "dur": 2021, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Utilities.ref.dll_9587680413563429.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748961752532864, "dur": 5683, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748961752539144, "dur": 2318, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Cursor.Editor.ref.dll_CD3F41468EC5D7E9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748961752541959, "dur": 1000, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748961752542992, "dur": 572, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748961752576738, "dur": 105, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748961752499894, "dur": 81719, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748961752581622, "dur": 2508704, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748961755090503, "dur": 823, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748961752499851, "dur": 81784, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752581639, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748961752581880, "dur": 407, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752582316, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752582662, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C9A2ED22AC71DAA3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748961752582841, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752582968, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DBF9BD3B0D79F91E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748961752583113, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752583340, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752583553, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_1D189CF611149EDC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748961752583679, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_EEFB9A6B4613348E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748961752583734, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752583871, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752584047, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_FF6DF2438A8881EC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748961752584114, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752584204, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_10C02A94B188B9C3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748961752584279, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752584502, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752584643, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752585002, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748961752585196, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748961752585424, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748961752585576, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752585783, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748961752585989, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752586075, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748961752586249, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748961752586800, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_C7D65B20A28C6399.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748961752586867, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752586965, "dur": 778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748961752587759, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752587917, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752588307, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752588502, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752588762, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752588924, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752589169, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752589543, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748961752589630, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752589811, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752589969, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752590055, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748961752590511, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752590640, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752590766, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752590943, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752591064, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752591188, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752591322, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752591497, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752591665, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752591784, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752592040, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752592241, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752592329, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752592497, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752592584, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752593317, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752594019, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752594750, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752595475, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752596117, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752596831, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752597582, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752598457, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748961752598787, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752598913, "dur": 4709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748961752603622, "dur": 579, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752604214, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752604271, "dur": 1248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748961752605544, "dur": 1778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748961752607356, "dur": 5843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748961752613200, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752613551, "dur": 1606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748961752615229, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748961752615984, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752616127, "dur": 3205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748961752619332, "dur": 615, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752619949, "dur": 2482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748961752622431, "dur": 416, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752622873, "dur": 4833, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748961752627717, "dur": 429, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752628149, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.Editor.ref.dll_94629FB7B2A83F68.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748961752628248, "dur": 764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748961752629012, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752629147, "dur": 2254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748961752631401, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752631593, "dur": 695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748961752632288, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752632365, "dur": 4968, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748961752637333, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752637671, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_C5B7873C632B963A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748961752637749, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752637884, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752638473, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752638955, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752639509, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752640066, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752640713, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752641377, "dur": 1923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752643300, "dur": 1233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752644533, "dur": 962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752645496, "dur": 1078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752646575, "dur": 1653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752648228, "dur": 1767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752649995, "dur": 397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752650393, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752650858, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752651081, "dur": 78, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752651159, "dur": 949, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752652108, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752653129, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752653489, "dur": 83, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752653573, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752653623, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752653920, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752654138, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752654328, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752654491, "dur": 121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752654612, "dur": 55, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752654667, "dur": 92, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752654759, "dur": 83, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752654842, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752655043, "dur": 59, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752655102, "dur": 75, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752655178, "dur": 94, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752655272, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752655527, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752655724, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752656160, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752656781, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752657342, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752658150, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752658856, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752659487, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752660094, "dur": 102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752660196, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752661400, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752661654, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752661867, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752662210, "dur": 57, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752662458, "dur": 1206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752663665, "dur": 83868, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752747534, "dur": 3038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748961752750573, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752750638, "dur": 3026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748961752753665, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752753733, "dur": 3358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748961752757091, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752757216, "dur": 3134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748961752760351, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752760457, "dur": 1943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748961752762401, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752762535, "dur": 3034, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748961752765570, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752765692, "dur": 3396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748961752769088, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748961752769172, "dur": 4606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748961752773819, "dur": 2316473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752499850, "dur": 81779, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752581634, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748961752581847, "dur": 441, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752582340, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752582735, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752582887, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_3DB4BB1D7CF8DED1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748961752582974, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752583119, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752583332, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752583526, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752583628, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_18DC2EF7A0439C61.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748961752583720, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752583869, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752584005, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_282E744BB5E921C6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748961752584061, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752584213, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752584284, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_B5FD6E098A39A5D2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748961752584335, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752584430, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752584622, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752584944, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752585120, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752585271, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_D9468BE09286D288.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748961752585359, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752585541, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752585657, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748961752585837, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752585982, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752586108, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748961752586320, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748961752586944, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752587053, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748961752587217, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752587388, "dur": 458, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752587903, "dur": 377, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752588283, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748961752588550, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752588706, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752588764, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752588918, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748961752589390, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752589628, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752589766, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752589852, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752589996, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752590352, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752590639, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752590737, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752590921, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752591037, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752591302, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748961752591633, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752591727, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752591837, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752592058, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752592194, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752592326, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752592481, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752592586, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752593342, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752594060, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752594769, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752595499, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752596249, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752596938, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752597735, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752598584, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748961752598861, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752599026, "dur": 5106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748961752604132, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752604385, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752604479, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748961752605147, "dur": 782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748961752605977, "dur": 3535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748961752609512, "dur": 441, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752609965, "dur": 2256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748961752612221, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752612431, "dur": 1180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748961752613638, "dur": 1938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748961752615576, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752615634, "dur": 926, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748961752616606, "dur": 823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748961752617471, "dur": 16906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748961752634377, "dur": 383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752634776, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752634838, "dur": 1574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748961752637403, "dur": 314, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 2, "ts": 1748961752637717, "dur": 1713, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 2, "ts": 1748961752639430, "dur": 590, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 2, "ts": 1748961752636450, "dur": 3571, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752640021, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752640670, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752641333, "dur": 1752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752643086, "dur": 1352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752644438, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752645482, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752646514, "dur": 1623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752648138, "dur": 1915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752650053, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752650398, "dur": 387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752650785, "dur": 52, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752650837, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752651076, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752651537, "dur": 2149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748961752653722, "dur": 3582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748961752657305, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752657605, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752657677, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752658453, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752659178, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752659768, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752659999, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752660364, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752661392, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752661659, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752662367, "dur": 96, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752662463, "dur": 1205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752663668, "dur": 83785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752747455, "dur": 2146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748961752749602, "dur": 510, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752750116, "dur": 2962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748961752753078, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752753136, "dur": 2456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748961752755592, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752755661, "dur": 2712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnity.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748961752758415, "dur": 2647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748961752761062, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752761189, "dur": 1857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748961752763046, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752763101, "dur": 1849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748961752764950, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752765031, "dur": 2772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748961752767805, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752767893, "dur": 2762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748961752770656, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752771357, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752771477, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752771599, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752771788, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752771850, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748961752772126, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748961752772244, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752772444, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752772539, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752772620, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752772745, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752772882, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752772994, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752773101, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961752773385, "dur": 2006751, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961754780389, "dur": 2250, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748961754780141, "dur": 6639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748961754788256, "dur": 585, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961755031183, "dur": 493, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748961754789343, "dur": 242350, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748961755034825, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748961755034822, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748961755034895, "dur": 328, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748961755035226, "dur": 55058, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752499858, "dur": 81784, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752581646, "dur": 630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748961752582276, "dur": 406, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752582682, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748961752582750, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752582884, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_94E7D34F0A24E8EE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748961752582976, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752583108, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752583237, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD7D90864D1DB0B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748961752583425, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752583495, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2D2E9A4895907F63.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748961752583546, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752583654, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_D09E55B94B6FBE69.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748961752583707, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752583890, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752584116, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752584196, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_4D1EEC228562B063.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748961752584361, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752584434, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752584590, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_57DA0DA15F72E273.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748961752584654, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752585008, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748961752585302, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752585486, "dur": 1610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748961752587097, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752587346, "dur": 407, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752587763, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748961752588045, "dur": 2264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748961752590309, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752590807, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748961752591475, "dur": 6547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748961752598023, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752598289, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_BD0E97F52A0DF729.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748961752598367, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752598448, "dur": 660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748961752599159, "dur": 4822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748961752603981, "dur": 1113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752605131, "dur": 1066, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748961752606230, "dur": 6502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748961752612734, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752613122, "dur": 1312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748961752614435, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752614591, "dur": 3336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748961752617927, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752618207, "dur": 3190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1748961752621471, "dur": 994, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752744831, "dur": 1529, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752622827, "dur": 123551, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1748961752747453, "dur": 3238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748961752750693, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752750811, "dur": 2446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748961752753257, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752753358, "dur": 3796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748961752757154, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752757222, "dur": 2592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748961752759814, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752759872, "dur": 3505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748961752763427, "dur": 2132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748961752765583, "dur": 2557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748961752768178, "dur": 2950, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748961752771129, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752771311, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752771682, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752771812, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752771977, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752772115, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748961752772413, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752772631, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752772766, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752772909, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752773111, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961752773674, "dur": 2315799, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748961755089488, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748961755089475, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748961755089567, "dur": 677, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748961752499865, "dur": 81786, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752581654, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BA0B4FF91CE5F8A1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748961752582266, "dur": 399, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752582667, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BA0B4FF91CE5F8A1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748961752582787, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752583061, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752583286, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_150D0801C80CAAF8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748961752583344, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752583480, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_150D0801C80CAAF8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748961752583541, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_B6303FFB36F7BFF8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748961752583692, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F44CFBD4B87405AF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748961752583746, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752583817, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F44CFBD4B87405AF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748961752583885, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752583997, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9191DFAD59FAEA6A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748961752584071, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752584176, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_3EDBDDC0989E0EE4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748961752584235, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752584344, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752584475, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752584591, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752584955, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752585128, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752585311, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748961752585441, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752585606, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748961752585853, "dur": 956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748961752586809, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752587168, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752587286, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748961752587435, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752587828, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752587908, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748961752588018, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752588298, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752588442, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1748961752588511, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752588743, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752588928, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752588990, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748961752589094, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748961752589592, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752589769, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752589940, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748961752590403, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748961752590558, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752590654, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752590776, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752590912, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752591021, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752591114, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752591255, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752591383, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752591493, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752591597, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752591684, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752591873, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752591975, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752592203, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752592347, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752592449, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752592527, "dur": 981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752593508, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752594240, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752594941, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752595645, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752596378, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752597097, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752597902, "dur": 806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752598708, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748961752598811, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752598916, "dur": 936, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748961752599852, "dur": 343, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752600293, "dur": 2803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748961752603097, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752603166, "dur": 3224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748961752606390, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752606653, "dur": 4703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748961752611356, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752611781, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752611837, "dur": 1509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748961752613386, "dur": 1495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748961752614881, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752614943, "dur": 4245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748961752619189, "dur": 854, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752620121, "dur": 2961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748961752623082, "dur": 381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752623468, "dur": 5609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748961752629077, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752629302, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752629633, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752629784, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748961752630134, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748961752630666, "dur": 3215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748961752633882, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752634220, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752634406, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752634508, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752635649, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752636105, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752636569, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752637047, "dur": 387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752637435, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752638164, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752638644, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752639163, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752639697, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752640347, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752640978, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752642082, "dur": 1876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752643958, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752644877, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752645963, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752647036, "dur": 2127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752649164, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752649997, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752650382, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752650811, "dur": 98, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752650909, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752651098, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752651741, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752652880, "dur": 1083, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748961752654012, "dur": 3716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748961752657743, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752657885, "dur": 3468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748961752661355, "dur": 416, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752661804, "dur": 1143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748961752662947, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752663005, "dur": 654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752663659, "dur": 1034, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752664693, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748961752664847, "dur": 82676, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752747524, "dur": 2129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748961752749653, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752749749, "dur": 3088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748961752752837, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752752905, "dur": 3332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748961752756237, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752756305, "dur": 1899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748961752758204, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752758262, "dur": 2520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748961752760782, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752760854, "dur": 3051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748961752763946, "dur": 2944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748961752766941, "dur": 1989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748961752768931, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752769028, "dur": 4302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748961752773374, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748961752773825, "dur": 2316451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752499871, "dur": 81808, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752581683, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A97ABBD6C696F706.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748961752582266, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752582714, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752582854, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_F3E4D7EAC2485D07.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748961752583061, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752583270, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_557EEB5E99C128BC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748961752583375, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752583532, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7FC5D5B6D1C0F474.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748961752583614, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752583682, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CF90820960D75094.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748961752583740, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752583829, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752584056, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_23B09F05B98149F7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748961752584107, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752584190, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_50D38B6F9A6D24D8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748961752584366, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752584462, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752584638, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752584985, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752585147, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752585301, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752585446, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752585590, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748961752585729, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752586093, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748961752586266, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1748961752586822, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752586958, "dur": 674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748961752587643, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752587930, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748961752588045, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752588306, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748961752588404, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748961752588473, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752588750, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752588873, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748961752589122, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748961752589476, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752589560, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748961752589627, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752589700, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1748961752589763, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752589937, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752590077, "dur": 454, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752590533, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752590732, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752590878, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752590933, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748961752591438, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752591591, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752591678, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752591850, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752592011, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752592114, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752592221, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752592343, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752592432, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752592580, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752593307, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752594000, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752594723, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752595446, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752596204, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752596894, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752597686, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752598535, "dur": 569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748961752599105, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752599305, "dur": 2489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748961752601794, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752602187, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Utilities.ref.dll_9587680413563429.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748961752602251, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752602326, "dur": 2165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748961752604526, "dur": 15045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748961752619571, "dur": 440, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752620015, "dur": 2244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748961752622259, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752622376, "dur": 4609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748961752626985, "dur": 393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752627382, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Shaders.ref.dll_050F300D13D769AC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748961752627460, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752627655, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752627771, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll_8EDD8A7C38E993CC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748961752627838, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752627918, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_F37C0F1BA2EC04B9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748961752627995, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752628060, "dur": 1215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748961752629303, "dur": 2609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748961752631912, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752632073, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748961752632231, "dur": 4024, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748961752636255, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752636562, "dur": 22216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748961752658779, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752659014, "dur": 2346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748961752661360, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752661526, "dur": 1424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748961752662950, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752663054, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752663607, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748961752663687, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748961752663992, "dur": 83481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752747476, "dur": 3935, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748961752751456, "dur": 3825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748961752755332, "dur": 3292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748961752758665, "dur": 2890, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748961752761556, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752761642, "dur": 3605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonanceEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748961752765247, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961752765308, "dur": 3961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748961752769290, "dur": 4339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748961752773667, "dur": 2261157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748961755034888, "dur": 353, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748961755035242, "dur": 55062, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752499878, "dur": 81808, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752581689, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748961752582315, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752582713, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752582983, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752583092, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752583290, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_B651CB6D5431C551.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748961752583348, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752583462, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752583557, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_D51B60744B48FB82.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748961752583700, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_2793B80490192449.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748961752583752, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752583866, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752584072, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_71635C60905BE81B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748961752584128, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752584209, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_9927031187632C7E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748961752584346, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752584511, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752584631, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752584989, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752585259, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4228628CD1B97490.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748961752585397, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752585623, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752585739, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748961752585884, "dur": 731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748961752586626, "dur": 952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748961752587595, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752587826, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748961752587960, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752588029, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748961752588405, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748961752588503, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748961752588662, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752588755, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752588902, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748961752589113, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752589439, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752589626, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752589785, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752590010, "dur": 465, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752590528, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752590717, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752590888, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752590975, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752591087, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752591178, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752591248, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752591480, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748961752591617, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752591756, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752591934, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748961752592049, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752592193, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752592297, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752592416, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752592504, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752593305, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752593989, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752594705, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752595437, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752596147, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752596853, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752597605, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752598499, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748961752598878, "dur": 17008, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748961752615887, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752616225, "dur": 859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748961752617110, "dur": 11811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748961752628921, "dur": 666, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752629632, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752629706, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748961752629820, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752629992, "dur": 3550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748961752633543, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752633788, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752633982, "dur": 2021, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748961752636003, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752636073, "dur": 14666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748961752650741, "dur": 561, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752651323, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752651386, "dur": 2331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748961752653717, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752653823, "dur": 8650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748961752662474, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752662736, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748961752662846, "dur": 617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748961752663463, "dur": 694, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752664175, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748961752664260, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748961752664536, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752664719, "dur": 82753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752747473, "dur": 2098, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748961752749571, "dur": 377, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752749951, "dur": 1988, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748961752751978, "dur": 2547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748961752754577, "dur": 2684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748961752757303, "dur": 2281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748961752759628, "dur": 3061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748961752762689, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752762756, "dur": 3008, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748961752765804, "dur": 2204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748961752768029, "dur": 2921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748961752770950, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752771068, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752771207, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752771401, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752771585, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752771858, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752771974, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752772104, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752772378, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752772633, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752772829, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752772957, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752773073, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752773127, "dur": 664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748961752773791, "dur": 2316534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752499884, "dur": 81807, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752581695, "dur": 589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752582284, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752582676, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752582744, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752582872, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FED4EADA5A496243.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752583109, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752583279, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752583338, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_73BBD5453873209A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752583574, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_5D1BAF9C8C0FFD1F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752583748, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752583872, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752583986, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_1CA607ABAD2FE49C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752584061, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752584124, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_548678B7412B99C9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752584243, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_D95259C03EE6A7C4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752584310, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752584486, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752584620, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752584936, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752585116, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752585280, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752585392, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752585552, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748961752585698, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748961752585957, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752586103, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748961752586708, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752586803, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752586894, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748961752587066, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748961752587213, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748961752587384, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748961752587580, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752587925, "dur": 478, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752588453, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748961752588906, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748961752589371, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752589567, "dur": 879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748961752590465, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748961752590554, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752590655, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752590770, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752590929, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752591046, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752591167, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752591241, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752591395, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748961752591481, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752591575, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752591657, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752591751, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752591926, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752592058, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752592236, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752592330, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752592474, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752592559, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752593330, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752594024, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752594735, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752595456, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752596194, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752596878, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752597651, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752598528, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752598868, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752599018, "dur": 1509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748961752600528, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752600766, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Searcher.Editor.ref.dll_188082AF4C893B1A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752600843, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752600905, "dur": 2814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752603720, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752604010, "dur": 4438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748961752608450, "dur": 743, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752609211, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752609313, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_70C63ADD63245F54.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752609399, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752609460, "dur": 2694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752612154, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752612434, "dur": 1569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752614046, "dur": 1706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752615779, "dur": 990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752616812, "dur": 780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752617640, "dur": 2778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748961752620419, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752620514, "dur": 2043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748961752622557, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752622866, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748961752622936, "dur": 3371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748961752626307, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752626465, "dur": 730, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752627265, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752627490, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rendering.LightTransport.Editor.ref.dll_B243E984A9C458BA.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752627567, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752627700, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_AAF6E30B590FA4FC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752627771, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752627836, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_A276D0F1D4D046E3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752627912, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752627984, "dur": 894, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752628916, "dur": 1608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748961752630524, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752630815, "dur": 2327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748961752633143, "dur": 437, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752633593, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752633648, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748961752633706, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752633787, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752633926, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748961752633993, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752634154, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752634271, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.CodeGen.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748961752634330, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752634429, "dur": 1209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752635718, "dur": 1647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752637366, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752637445, "dur": 13842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748961752651287, "dur": 704, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752651997, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Runtime.ref.dll_C4A720C3D568CE76.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752652096, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752652267, "dur": 1886, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752654188, "dur": 4638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748961752658826, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752659097, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752659727, "dur": 81, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752659841, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752660123, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752660569, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752660967, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752661635, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752662466, "dur": 1142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752663608, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748961752663688, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748961752663970, "dur": 83486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752747457, "dur": 2588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748961752750045, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752750129, "dur": 3517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748961752753647, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752753846, "dur": 2282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonance.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748961752756128, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752756205, "dur": 3293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748961752759523, "dur": 2673, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748961752762244, "dur": 3613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748961752765857, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748961752765940, "dur": 3731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748961752769724, "dur": 4030, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748961752773801, "dur": 2316534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752499891, "dur": 81812, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752581706, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_9CEC3B310263C3AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748961752582277, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752582661, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CD898CC368A4D4E9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748961752582860, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752583097, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752583268, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_EE2B3985795E2BC5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748961752583520, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752583620, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_CC9B8F296E9C6231.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748961752583684, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752583832, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752584000, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_13682AC6E2C4EE7B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748961752584058, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752584184, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_AB798D326E6716CC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748961752584342, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752584494, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752584599, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_4E33C3C55AB9064E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748961752584665, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752584993, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748961752585237, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752585330, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748961752585595, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748961752585852, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752586078, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748961752586312, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748961752586894, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752587158, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752587217, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748961752587319, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748961752587386, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748961752587726, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752587942, "dur": 328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752588273, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748961752588565, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752588767, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752588943, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752589113, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748961752589349, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752589585, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752589791, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752589911, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752589980, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748961752590167, "dur": 393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752590565, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752590738, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752590908, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752591040, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752591126, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752591363, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752591542, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752591633, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752591740, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752591961, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752592086, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752592265, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752592364, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752592504, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752592603, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752593364, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752594073, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752594792, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752595512, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752596253, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752596940, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752597745, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752598625, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748961752598826, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752598991, "dur": 2580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748961752601572, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752601799, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.ref.dll_62FEAEE8B284189E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748961752601875, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752601959, "dur": 1981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748961752603941, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752604016, "dur": 4116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748961752608132, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752608434, "dur": 12035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748961752620469, "dur": 880, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752621373, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752621554, "dur": 4166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748961752625720, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752626007, "dur": 1305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748961752627332, "dur": 3981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748961752631313, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752631417, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748961752632083, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752632139, "dur": 3016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748961752635155, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752635325, "dur": 1591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748961752636950, "dur": 24293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748961752661243, "dur": 611, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752661927, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748961752662530, "dur": 812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748961752663343, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752663605, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748961752663684, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748961752663993, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752664238, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748961752664318, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748961752664540, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752664671, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748961752664806, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748961754772608, "dur": 60, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961752665641, "dur": 2107065, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748961754780222, "dur": 184, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748961754779410, "dur": 1184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748961754780924, "dur": 2000, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961754784735, "dur": 183156, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748961754968599, "dur": 1261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748961754970374, "dur": 200, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961755086204, "dur": 300, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748961754970906, "dur": 115606, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748961755089526, "dur": 716, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748961755093105, "dur": 1667, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 23505, "tid": 125, "ts": 1748961755109718, "dur": 1751, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 23505, "tid": 125, "ts": 1748961755111503, "dur": 1746, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 23505, "tid": 125, "ts": 1748961755104258, "dur": 10733, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}