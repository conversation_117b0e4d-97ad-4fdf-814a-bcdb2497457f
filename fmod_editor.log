[LOG] system_create                            : Header version = 2.03.06. Current version = 2.03.06.
[LOG] Manager::init                            : maxchannels = 256 studioflags = 00000006 flags 00000000 extradriverdata 0x0.
[LOG] SystemI::init                            : Initialize version=20306 (149358), maxchannels=256, flags=0x00020000
[LOG] SystemI::setOutputInternal               : Setting output to 'FMOD Core Audio Output'
[LOG] OutputCoreAudio::init                    : Output running 2 channels at 44100Hz sample rate.
[LOG] OutputCoreAudio::init                    : Maximum hardware read size: 512 samples, Software buffer size: 512 samples, Number of software buffers: 4.
[LOG] SystemI::init                            : Output requires a sample rate of 44100Hz, resampling will occur.
[LOG] Thread::initThread                       : Init FMOD stream thread. Affinity: 0x4000000000000003, Priority: 0xFFFF7FFB, Stack Size: 98304, Semaphore: No, Sleep Time: 10, Looping: Yes.
[LOG] SystemI::DSPCodecPoolRegister            : register codec pool for pool type 0
[LOG] Thread::initThread                       : Init FMOD mixer thread. Affinity: 0x4000000000000001, Priority: 0xFFFF7FFA, Stack Size: 81920, Semaphore: Yes, Sleep Time: 0, Looping: Yes.
[LOG] AsyncManager::init                       : manager 0x31dd00608 isAsync 0 updatePeriod 0.02
[LOG] AsyncManager::init                       : done
[LOG] PlaybackSystem::init                     : 
[LOG] Thread::initThread                       : Init FMOD Studio sample load thread. Affinity: 0x4000000000000003, Priority: 0xFFFF7FFD, Stack Size: 98304, Semaphore: No, Sleep Time: 1, Looping: No.
[LOG] PlaybackSystem::init                     : done
[LOG] Thread::initThread                       : Init FMOD Studio bank load thread. Affinity: 0x4000000000000003, Priority: 0xFFFF7FFD, Stack Size: 98304, Semaphore: No, Sleep Time: 1, Looping: No.
[LOG] Manager::init                            : done.
[LOG] BankLoader::serializeBank                : fileversion = 146, compatVersion = 146 (oldest = 44, newest = 146)
[LOG] BankLoader::serializeBank                : fileversion = 146, compatVersion = 146 (oldest = 44, newest = 146)
[LOG] BankLoader::serializeBank                : fileversion = 146, compatVersion = 146 (oldest = 44, newest = 146)
[LOG] PlaybackSystem::acquireMasterBus         : Setting master channel group format to 6
[LOG] BankLoader::serializeBank                : fileversion = 146, compatVersion = 146 (oldest = 44, newest = 146)
